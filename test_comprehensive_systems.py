#!/usr/bin/env python3
"""
Comprehensive Test Suite for Advanced Security Features, Notification System, and Queue System
Testing all three systems with complete functionality validation
"""

import requests
import json
import time
import random
import string
from datetime import datetime, timedelta

# Configuration
BASE_URL = "http://localhost:8001"
API_BASE = f"{BASE_URL}/api"

class ComprehensiveSystemTester:
    def __init__(self):
        self.session = requests.Session()
        self.test_user_token = None
        self.test_user_id = None
        self.test_user_email = None
        self.results = {
            'security_features': {},
            'notification_system': {},
            'queue_system': {},
            'overall_status': 'PENDING'
        }

    def generate_test_email(self):
        """Generate unique test email"""
        timestamp = int(time.time())
        random_str = ''.join(random.choices(string.ascii_lowercase, k=6))
        return f"test-{timestamp}-{random_str}@example.com"

    def print_section(self, title):
        """Print section header"""
        print(f"\n{'='*80}")
        print(f"🧪 {title}")
        print(f"{'='*80}")

    def print_test(self, test_name, status, details=""):
        """Print test result"""
        status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_icon} {test_name}: {status}")
        if details:
            print(f"   Details: {details}")

    def setup_test_user(self):
        """Create test user for testing"""
        try:
            self.test_user_email = self.generate_test_email()
            
            # Register test user
            register_data = {
                "email": self.test_user_email,
                "password": "TestPassword123!",
                "password_confirmation": "TestPassword123!",
                "name": "Test User"
            }
            
            response = self.session.post(f"{API_BASE}/auth/register", json=register_data)
            
            if response.status_code == 201:
                data = response.json()
                self.test_user_token = data.get('token')
                self.test_user_id = data.get('user', {}).get('id')
                
                # Set authorization header
                self.session.headers.update({
                    'Authorization': f'Bearer {self.test_user_token}'
                })
                
                print(f"✅ Test user created: {self.test_user_email} (ID: {self.test_user_id})")
                return True
            else:
                print(f"❌ Failed to create test user: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Test user setup error: {str(e)}")
            return False

    # ==================== ADVANCED SECURITY FEATURES TESTS ====================
    
    def test_security_features(self):
        """Test Advanced Security Features System"""
        self.print_section("ADVANCED SECURITY FEATURES TESTING")
        
        security_results = {}
        
        # Test 1: Security Events Logging
        security_results['security_events_logging'] = self.test_security_events_logging()
        
        # Test 2: Account Lockout Mechanism
        security_results['account_lockout'] = self.test_account_lockout()
        
        # Test 3: Suspicious Activity Detection
        security_results['suspicious_activity'] = self.test_suspicious_activity_detection()
        
        # Test 4: Security Monitoring Dashboard
        security_results['security_monitoring'] = self.test_security_monitoring()
        
        # Test 5: Rate Limit Violation Tracking
        security_results['rate_limit_tracking'] = self.test_rate_limit_tracking()
        
        # Test 6: Security Event Analysis
        security_results['security_analysis'] = self.test_security_event_analysis()
        
        self.results['security_features'] = security_results
        
        # Calculate overall security score
        passed_tests = sum(1 for result in security_results.values() if result == "PASS")
        total_tests = len(security_results)
        security_score = (passed_tests / total_tests) * 100
        
        print(f"\n🔒 SECURITY FEATURES SUMMARY: {passed_tests}/{total_tests} tests passed ({security_score:.1f}%)")
        return security_score >= 80

    def test_security_events_logging(self):
        """Test security events logging functionality"""
        try:
            # Test security events endpoint
            response = self.session.get(f"{API_BASE}/security/events")
            
            if response.status_code == 200:
                events = response.json()
                self.print_test("Security Events Logging", "PASS", f"Retrieved {len(events)} events")
                return "PASS"
            else:
                self.print_test("Security Events Logging", "FAIL", f"Status: {response.status_code}")
                return "FAIL"
                
        except Exception as e:
            self.print_test("Security Events Logging", "FAIL", str(e))
            return "FAIL"

    def test_account_lockout(self):
        """Test account lockout mechanism"""
        try:
            # Test account status check
            response = self.session.get(f"{API_BASE}/security/account-status")
            
            if response.status_code == 200:
                status = response.json()
                account_locked = status.get('account_locked', False)
                login_attempts = status.get('login_attempts', 0)
                
                self.print_test("Account Lockout Check", "PASS", 
                              f"Locked: {account_locked}, Attempts: {login_attempts}")
                return "PASS"
            else:
                self.print_test("Account Lockout Check", "FAIL", f"Status: {response.status_code}")
                return "FAIL"
                
        except Exception as e:
            self.print_test("Account Lockout Check", "FAIL", str(e))
            return "FAIL"

    def test_suspicious_activity_detection(self):
        """Test suspicious activity detection"""
        try:
            # Test suspicious activity endpoint
            response = self.session.get(f"{API_BASE}/security/suspicious-activity")
            
            if response.status_code == 200:
                activities = response.json()
                self.print_test("Suspicious Activity Detection", "PASS", 
                              f"Found {len(activities)} suspicious activities")
                return "PASS"
            else:
                self.print_test("Suspicious Activity Detection", "FAIL", f"Status: {response.status_code}")
                return "FAIL"
                
        except Exception as e:
            self.print_test("Suspicious Activity Detection", "FAIL", str(e))
            return "FAIL"

    def test_security_monitoring(self):
        """Test security monitoring dashboard"""
        try:
            # Test security dashboard endpoint
            response = self.session.get(f"{API_BASE}/security/dashboard")
            
            if response.status_code == 200:
                dashboard = response.json()
                self.print_test("Security Monitoring Dashboard", "PASS", 
                              f"Dashboard data retrieved successfully")
                return "PASS"
            else:
                self.print_test("Security Monitoring Dashboard", "FAIL", f"Status: {response.status_code}")
                return "FAIL"
                
        except Exception as e:
            self.print_test("Security Monitoring Dashboard", "FAIL", str(e))
            return "FAIL"

    def test_rate_limit_tracking(self):
        """Test rate limit violation tracking"""
        try:
            # Make multiple rapid requests to trigger rate limiting
            for i in range(5):
                response = self.session.get(f"{API_BASE}/auth/profile")
                time.sleep(0.1)  # Small delay
            
            # Check if rate limiting headers are present
            if 'X-RateLimit-Limit' in response.headers:
                self.print_test("Rate Limit Tracking", "PASS", 
                              f"Rate limit headers present")
                return "PASS"
            else:
                self.print_test("Rate Limit Tracking", "WARN", 
                              "Rate limit headers not found")
                return "WARN"
                
        except Exception as e:
            self.print_test("Rate Limit Tracking", "FAIL", str(e))
            return "FAIL"

    def test_security_event_analysis(self):
        """Test security event analysis"""
        try:
            # Test security analysis endpoint
            response = self.session.get(f"{API_BASE}/security/analysis")
            
            if response.status_code == 200:
                analysis = response.json()
                self.print_test("Security Event Analysis", "PASS", 
                              "Security analysis completed")
                return "PASS"
            elif response.status_code == 404:
                self.print_test("Security Event Analysis", "WARN", 
                              "Analysis endpoint not implemented")
                return "WARN"
            else:
                self.print_test("Security Event Analysis", "FAIL", f"Status: {response.status_code}")
                return "FAIL"
                
        except Exception as e:
            self.print_test("Security Event Analysis", "FAIL", str(e))
            return "FAIL"

    # ==================== NOTIFICATION SYSTEM TESTS ====================
    
    def test_notification_system(self):
        """Test Notification System"""
        self.print_section("NOTIFICATION SYSTEM TESTING")
        
        notification_results = {}
        
        # Test 1: Email Notifications
        notification_results['email_notifications'] = self.test_email_notifications()
        
        # Test 2: Database Notifications
        notification_results['database_notifications'] = self.test_database_notifications()
        
        # Test 3: SMS Notifications
        notification_results['sms_notifications'] = self.test_sms_notifications()
        
        # Test 4: OTP Notifications
        notification_results['otp_notifications'] = self.test_otp_notifications()
        
        # Test 5: Security Alert Notifications
        notification_results['security_alerts'] = self.test_security_alert_notifications()
        
        # Test 6: Account Change Notifications
        notification_results['account_changes'] = self.test_account_change_notifications()
        
        self.results['notification_system'] = notification_results
        
        # Calculate overall notification score
        passed_tests = sum(1 for result in notification_results.values() if result == "PASS")
        total_tests = len(notification_results)
        notification_score = (passed_tests / total_tests) * 100
        
        print(f"\n📧 NOTIFICATION SYSTEM SUMMARY: {passed_tests}/{total_tests} tests passed ({notification_score:.1f}%)")
        return notification_score >= 80

    def test_email_notifications(self):
        """Test email notification functionality"""
        try:
            # Test sending email notification
            test_data = {
                'type': 'account_change',
                'message': 'Test email notification'
            }
            
            response = self.session.post(f"{API_BASE}/notifications/test", json=test_data)
            
            if response.status_code == 200:
                result = response.json()
                self.print_test("Email Notifications", "PASS", "Email notification sent successfully")
                return "PASS"
            else:
                self.print_test("Email Notifications", "FAIL", f"Status: {response.status_code}")
                return "FAIL"
                
        except Exception as e:
            self.print_test("Email Notifications", "FAIL", str(e))
            return "FAIL"

    def test_database_notifications(self):
        """Test database notification storage"""
        try:
            # Get user notifications
            response = self.session.get(f"{API_BASE}/notifications")
            
            if response.status_code == 200:
                notifications = response.json()
                self.print_test("Database Notifications", "PASS", 
                              f"Retrieved {len(notifications)} notifications")
                return "PASS"
            else:
                self.print_test("Database Notifications", "FAIL", f"Status: {response.status_code}")
                return "FAIL"
                
        except Exception as e:
            self.print_test("Database Notifications", "FAIL", str(e))
            return "FAIL"

    def test_sms_notifications(self):
        """Test SMS notification functionality"""
        try:
            # Test SMS notification (will likely be simulated)
            test_data = {
                'type': 'security_alert',
                'message': 'Test SMS notification'
            }
            
            response = self.session.post(f"{API_BASE}/notifications/test", json=test_data)
            
            if response.status_code == 200:
                self.print_test("SMS Notifications", "PASS", "SMS notification processed")
                return "PASS"
            else:
                self.print_test("SMS Notifications", "WARN", "SMS service may not be configured")
                return "WARN"
                
        except Exception as e:
            self.print_test("SMS Notifications", "FAIL", str(e))
            return "FAIL"

    def test_otp_notifications(self):
        """Test OTP notification functionality"""
        try:
            # Test OTP notification
            test_data = {
                'type': 'otp',
                'message': 'Test OTP notification'
            }
            
            response = self.session.post(f"{API_BASE}/notifications/test", json=test_data)
            
            if response.status_code == 200:
                self.print_test("OTP Notifications", "PASS", "OTP notification sent")
                return "PASS"
            else:
                self.print_test("OTP Notifications", "FAIL", f"Status: {response.status_code}")
                return "FAIL"
                
        except Exception as e:
            self.print_test("OTP Notifications", "FAIL", str(e))
            return "FAIL"

    def test_security_alert_notifications(self):
        """Test security alert notifications"""
        try:
            # Test security alert notification
            test_data = {
                'type': 'security_alert',
                'message': 'Test security alert notification'
            }
            
            response = self.session.post(f"{API_BASE}/notifications/test", json=test_data)
            
            if response.status_code == 200:
                self.print_test("Security Alert Notifications", "PASS", "Security alert sent")
                return "PASS"
            else:
                self.print_test("Security Alert Notifications", "FAIL", f"Status: {response.status_code}")
                return "FAIL"
                
        except Exception as e:
            self.print_test("Security Alert Notifications", "FAIL", str(e))
            return "FAIL"

    def test_account_change_notifications(self):
        """Test account change notifications"""
        try:
            # Test account change notification
            test_data = {
                'type': 'account_change',
                'message': 'Test account change notification'
            }
            
            response = self.session.post(f"{API_BASE}/notifications/test", json=test_data)
            
            if response.status_code == 200:
                self.print_test("Account Change Notifications", "PASS", "Account change notification sent")
                return "PASS"
            else:
                self.print_test("Account Change Notifications", "FAIL", f"Status: {response.status_code}")
                return "FAIL"
                
        except Exception as e:
            self.print_test("Account Change Notifications", "FAIL", str(e))
            return "FAIL"

    # ==================== QUEUE SYSTEM TESTS ====================
    
    def test_queue_system(self):
        """Test Queue System"""
        self.print_section("QUEUE SYSTEM TESTING")
        
        queue_results = {}
        
        # Test 1: Queue Status and Health
        queue_results['queue_status'] = self.test_queue_status()
        
        # Test 2: Email Job Queuing
        queue_results['email_jobs'] = self.test_email_job_queuing()
        
        # Test 3: Security Event Processing Jobs
        queue_results['security_jobs'] = self.test_security_event_jobs()
        
        # Test 4: Data Cleanup Jobs
        queue_results['cleanup_jobs'] = self.test_data_cleanup_jobs()
        
        # Test 5: Queue Statistics
        queue_results['queue_stats'] = self.test_queue_statistics()
        
        # Test 6: Failed Job Handling
        queue_results['failed_jobs'] = self.test_failed_job_handling()
        
        self.results['queue_system'] = queue_results
        
        # Calculate overall queue score
        passed_tests = sum(1 for result in queue_results.values() if result == "PASS")
        total_tests = len(queue_results)
        queue_score = (passed_tests / total_tests) * 100
        
        print(f"\n⚙️ QUEUE SYSTEM SUMMARY: {passed_tests}/{total_tests} tests passed ({queue_score:.1f}%)")
        return queue_score >= 80

    def test_queue_status(self):
        """Test queue system status"""
        try:
            response = self.session.get(f"{API_BASE}/queue/status")
            
            if response.status_code == 200:
                status = response.json()
                self.print_test("Queue Status", "PASS", "Queue system operational")
                return "PASS"
            else:
                self.print_test("Queue Status", "FAIL", f"Status: {response.status_code}")
                return "FAIL"
                
        except Exception as e:
            self.print_test("Queue Status", "FAIL", str(e))
            return "FAIL"

    def test_email_job_queuing(self):
        """Test email job queuing"""
        try:
            test_data = {
                'email_type': 'welcome',
                'message': 'Test email job'
            }
            
            response = self.session.post(f"{API_BASE}/queue/test-email", json=test_data)
            
            if response.status_code == 200:
                self.print_test("Email Job Queuing", "PASS", "Email job queued successfully")
                return "PASS"
            else:
                self.print_test("Email Job Queuing", "FAIL", f"Status: {response.status_code}")
                return "FAIL"
                
        except Exception as e:
            self.print_test("Email Job Queuing", "FAIL", str(e))
            return "FAIL"

    def test_security_event_jobs(self):
        """Test security event processing jobs"""
        try:
            response = self.session.post(f"{API_BASE}/queue/test-security-processing")
            
            if response.status_code == 200:
                self.print_test("Security Event Jobs", "PASS", "Security processing job queued")
                return "PASS"
            else:
                self.print_test("Security Event Jobs", "FAIL", f"Status: {response.status_code}")
                return "FAIL"
                
        except Exception as e:
            self.print_test("Security Event Jobs", "FAIL", str(e))
            return "FAIL"

    def test_data_cleanup_jobs(self):
        """Test data cleanup jobs"""
        try:
            response = self.session.post(f"{API_BASE}/queue/test-cleanup")
            
            if response.status_code == 200:
                self.print_test("Data Cleanup Jobs", "PASS", "Cleanup job queued")
                return "PASS"
            else:
                self.print_test("Data Cleanup Jobs", "FAIL", f"Status: {response.status_code}")
                return "FAIL"
                
        except Exception as e:
            self.print_test("Data Cleanup Jobs", "FAIL", str(e))
            return "FAIL"

    def test_queue_statistics(self):
        """Test queue statistics"""
        try:
            response = self.session.get(f"{API_BASE}/queue/stats")
            
            if response.status_code == 200:
                stats = response.json()
                self.print_test("Queue Statistics", "PASS", "Queue statistics retrieved")
                return "PASS"
            else:
                self.print_test("Queue Statistics", "FAIL", f"Status: {response.status_code}")
                return "FAIL"
                
        except Exception as e:
            self.print_test("Queue Statistics", "FAIL", str(e))
            return "FAIL"

    def test_failed_job_handling(self):
        """Test failed job handling"""
        try:
            response = self.session.get(f"{API_BASE}/queue/failed-jobs")
            
            if response.status_code == 200:
                failed_jobs = response.json()
                self.print_test("Failed Job Handling", "PASS", f"Found {len(failed_jobs)} failed jobs")
                return "PASS"
            else:
                self.print_test("Failed Job Handling", "FAIL", f"Status: {response.status_code}")
                return "FAIL"
                
        except Exception as e:
            self.print_test("Failed Job Handling", "FAIL", str(e))
            return "FAIL"

    # ==================== MAIN TEST EXECUTION ====================
    
    def run_all_tests(self):
        """Run all comprehensive tests"""
        print("🚀 Starting Comprehensive System Testing")
        print(f"📍 Testing against: {BASE_URL}")
        print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Setup test user
        if not self.setup_test_user():
            print("❌ Failed to setup test user. Aborting tests.")
            return False
        
        # Run all test suites
        security_passed = self.test_security_features()
        notification_passed = self.test_notification_system()
        queue_passed = self.test_queue_system()
        
        # Calculate overall results
        overall_passed = security_passed and notification_passed and queue_passed
        self.results['overall_status'] = 'PASS' if overall_passed else 'FAIL'
        
        # Print final summary
        self.print_final_summary()
        
        return overall_passed

    def print_final_summary(self):
        """Print final test summary"""
        self.print_section("COMPREHENSIVE TEST SUMMARY")
        
        # Security Features Summary
        security_results = self.results['security_features']
        security_passed = sum(1 for r in security_results.values() if r == "PASS")
        security_total = len(security_results)
        
        print(f"🔒 Advanced Security Features: {security_passed}/{security_total} tests passed")
        for test, result in security_results.items():
            status_icon = "✅" if result == "PASS" else "❌" if result == "FAIL" else "⚠️"
            print(f"   {status_icon} {test.replace('_', ' ').title()}: {result}")
        
        # Notification System Summary
        notification_results = self.results['notification_system']
        notification_passed = sum(1 for r in notification_results.values() if r == "PASS")
        notification_total = len(notification_results)
        
        print(f"\n📧 Notification System: {notification_passed}/{notification_total} tests passed")
        for test, result in notification_results.items():
            status_icon = "✅" if result == "PASS" else "❌" if result == "FAIL" else "⚠️"
            print(f"   {status_icon} {test.replace('_', ' ').title()}: {result}")
        
        # Queue System Summary
        queue_results = self.results['queue_system']
        queue_passed = sum(1 for r in queue_results.values() if r == "PASS")
        queue_total = len(queue_results)
        
        print(f"\n⚙️ Queue System: {queue_passed}/{queue_total} tests passed")
        for test, result in queue_results.items():
            status_icon = "✅" if result == "PASS" else "❌" if result == "FAIL" else "⚠️"
            print(f"   {status_icon} {test.replace('_', ' ').title()}: {result}")
        
        # Overall Summary
        total_passed = security_passed + notification_passed + queue_passed
        total_tests = security_total + notification_total + queue_total
        overall_score = (total_passed / total_tests) * 100 if total_tests > 0 else 0
        
        print(f"\n🎯 OVERALL RESULTS:")
        print(f"   Total Tests: {total_tests}")
        print(f"   Passed: {total_passed}")
        print(f"   Success Rate: {overall_score:.1f}%")
        print(f"   Status: {self.results['overall_status']}")
        
        if overall_score >= 90:
            print("🏆 EXCELLENT: All systems are working perfectly!")
        elif overall_score >= 80:
            print("✅ GOOD: Systems are working well with minor issues")
        elif overall_score >= 60:
            print("⚠️ FAIR: Systems need some fixes")
        else:
            print("❌ POOR: Systems require significant fixes")

if __name__ == "__main__":
    tester = ComprehensiveSystemTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 All systems tested successfully!")
        exit(0)
    else:
        print("\n⚠️ Some systems need attention!")
        exit(1)
