from masonite.notification import Notification
from masonite.mail import Mailable
from masonite.queues import Queueable


class AccountChange(Notification, Mailable, Queueable):
    """Account change notification for profile updates, security changes, etc."""

    def __init__(self, change_type, description, details=None, requires_action=False):
        self.change_type = change_type
        self.description = description
        self.details = details or {}
        self.requires_action = requires_action

    def to_mail(self, notifiable):
        """Send account change notification via email"""
        subject = f"Account Update: {self.change_type}"

        if self.requires_action:
            subject = f"Action Required: {self.change_type}"

        return (
            self.to(notifiable.email)
            .subject(subject)
            .from_("<EMAIL>")
            .text(self._build_email_content(notifiable))
        )

    def to_database(self, notifiable):
        """Store account change notification in database"""
        return {
            'type': 'account_change',
            'change_type': self.change_type,
            'description': self.description,
            'details': self.details,
            'requires_action': self.requires_action,
            'action_url': '/account/settings',
            'icon': self._get_change_icon()
        }

    def via(self, notifiable):
        """Determine notification channels"""
        return ["database", "mail"]

    def _build_email_content(self, notifiable):
        """Build email content for account change"""
        content = f"""
Dear {getattr(notifiable, 'name', 'User')},

Your account has been updated:

Change: {self.change_type}
Description: {self.description}

"""

        if self.details:
            content += "Details:\n"
            for key, value in self.details.items():
                content += f"- {key.replace('_', ' ').title()}: {value}\n"

        if self.requires_action:
            content += """

ACTION REQUIRED:
Please log in to your account to complete this change or verify the update.
"""

        content += """

If you did not make this change, please contact our support team immediately.

You can manage your account settings here: https://secureapp.com/account/settings

Best regards,
SecureApp Team

---
This is an automated notification. Please do not reply to this email.
"""

        return content

    def _get_change_icon(self):
        """Get icon based on change type"""
        icons = {
            'password_changed': '🔑',
            'email_changed': '📧',
            'phone_changed': '📱',
            'profile_updated': '👤',
            'two_factor_enabled': '🔐',
            'two_factor_disabled': '🔓',
            'account_verified': '✅',
            'account_locked': '🔒',
            'account_unlocked': '🔓'
        }
        return icons.get(self.change_type, '📝')
