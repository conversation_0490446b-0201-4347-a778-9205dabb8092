from masonite.notification import Notification
from masonite.mail import Mailable
from masonite.queues import Queueable


class OTPNotification(Notification, Mailable, Queueable):
    """OTP notification for sending one-time passwords via email and SMS"""

    def __init__(self, otp_code, otp_type="login", expires_in_minutes=10):
        self.otp_code = otp_code
        self.otp_type = otp_type
        self.expires_in_minutes = expires_in_minutes

    def to_mail(self, notifiable):
        """Send OTP via email"""
        subject = f"Your verification code: {self.otp_code}"

        if self.otp_type == "login":
            subject = f"Your login code: {self.otp_code}"
        elif self.otp_type == "2fa":
            subject = f"Your 2FA code: {self.otp_code}"

        return (
            self.to(notifiable.email)
            .subject(subject)
            .from_("<EMAIL>")
            .text(self._build_email_content(notifiable))
        )

    def to_vonage(self, notifiable):
        """Send OTP via SMS"""
        from masonite.notification.components import Sms

        message = f"Your verification code is: {self.otp_code}. Valid for {self.expires_in_minutes} minutes. Do not share this code."

        if self.otp_type == "login":
            message = f"Your login code is: {self.otp_code}. Valid for {self.expires_in_minutes} minutes."

        return Sms().text(message)

    def to_database(self, notifiable):
        """Store OTP notification in database (for audit purposes)"""
        return {
            'type': 'otp_sent',
            'otp_type': self.otp_type,
            'expires_in_minutes': self.expires_in_minutes,
            'sent_to': getattr(notifiable, 'email', 'unknown'),
            'icon': '🔑'
        }

    def via(self, notifiable):
        """Determine notification channels"""
        # Default to email
        channels = ["mail", "database"]

        # Add SMS if phone number is available and verified
        if hasattr(notifiable, 'phone') and notifiable.phone:
            channels.append("vonage")

        return channels

    def _build_email_content(self, notifiable):
        """Build email content for OTP"""
        purpose_text = {
            'login': 'sign in to your account',
            'verification': 'verify your account',
            '2fa': 'complete two-factor authentication'
        }

        purpose = purpose_text.get(self.otp_type, 'verify your identity')

        content = f"""
Dear {getattr(notifiable, 'name', 'User')},

Your verification code to {purpose} is:

{self.otp_code}

This code will expire in {self.expires_in_minutes} minutes.

For your security:
- Do not share this code with anyone
- Only enter this code on the official SecureApp website
- If you didn't request this code, please ignore this email

If you're having trouble, please contact our support team.

Best regards,
SecureApp Team

---
This is an automated message. Please do not reply to this email.
"""

        return content
