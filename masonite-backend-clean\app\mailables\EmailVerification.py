from masonite.mail import Mailable
from masonite.environment import env


class EmailVerification(Mailable):
    """
    Email Verification Mailable
    Compatible with LoopBack email verification flow
    """
    
    def __init__(self, user, token):
        """Initialize with user and verification token"""
        self.user = user
        self.token = token
    
    def build(self):
        """Build the email verification email"""
        verification_url = f"{env('FRONTEND_URL', 'http://localhost:4200')}/auth/verify-email?token={self.token}"
        
        html_content = f"""
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #333;">Verify Your Email Address</h2>
            <p>Hello {self.user.name},</p>
            <p>Thank you for registering with our secure application. Please click the button below to verify your email address:</p>
            <div style="text-align: center; margin: 30px 0;">
                <a href="{verification_url}"
                   style="background-color: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                    Verify Email
                </a>
            </div>
            <p>If the button doesn't work, copy and paste this link into your browser:</p>
            <p style="word-break: break-all; color: #666;">{verification_url}</p>
            <p style="color: #666; font-size: 12px;">This link will expire in 24 hours.</p>
            <p style="color: #666; font-size: 12px;">If you didn't create an account, please ignore this email.</p>
        </div>
        """
        
        text_content = f"""
        Verify Your Email Address
        
        Hello {self.user.name},
        
        Thank you for registering with our secure application. Please click the link below to verify your email address:
        
        {verification_url}
        
        This link will expire in 24 hours.
        
        If you didn't create an account, please ignore this email.
        """
        
        return (
            self.to(self.user.email)
            .subject("Verify Your Email Address")
            .from_(env("EMAIL_FROM", "<EMAIL>"))
            .text(text_content)
            .html(html_content)
        )
