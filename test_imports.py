#!/usr/bin/env python3
"""
Test imports to find the issue
"""

def test_imports():
    """Test importing all the notification-related modules"""
    
    print("🔍 Testing imports...")
    
    try:
        print("1. Testing masonite.facades import...")
        from masonite.facades import Notification
        print("   ✅ masonite.facades.Notification imported successfully")
    except Exception as e:
        print(f"   ❌ masonite.facades.Notification import failed: {str(e)}")
        return False
    
    try:
        print("2. Testing NotificationService import...")
        from app.services.NotificationService import NotificationService
        print("   ✅ NotificationService imported successfully")
    except Exception as e:
        print(f"   ❌ NotificationService import failed: {str(e)}")
        return False
    
    try:
        print("3. Testing NotificationService instantiation...")
        service = NotificationService()
        print("   ✅ NotificationService instantiated successfully")
    except Exception as e:
        print(f"   ❌ NotificationService instantiation failed: {str(e)}")
        return False
    
    try:
        print("4. Testing SecurityAlert import...")
        from app.notifications.SecurityAlert import SecurityAlert
        print("   ✅ SecurityAlert imported successfully")
    except Exception as e:
        print(f"   ❌ SecurityAlert import failed: {str(e)}")
        return False
    
    try:
        print("5. Testing AccountChange import...")
        from app.notifications.AccountChange import AccountChange
        print("   ✅ AccountChange imported successfully")
    except Exception as e:
        print(f"   ❌ AccountChange import failed: {str(e)}")
        return False
    
    try:
        print("6. Testing OTPNotification import...")
        from app.notifications.OTPNotification import OTPNotification
        print("   ✅ OTPNotification imported successfully")
    except Exception as e:
        print(f"   ❌ OTPNotification import failed: {str(e)}")
        return False
    
    try:
        print("7. Testing SecurityAlert instantiation...")
        alert = SecurityAlert("Test Alert", "Test message", {}, "low")
        print("   ✅ SecurityAlert instantiated successfully")
    except Exception as e:
        print(f"   ❌ SecurityAlert instantiation failed: {str(e)}")
        return False
    
    try:
        print("8. Testing NotificationController import...")
        from app.controllers.NotificationController import NotificationController
        print("   ✅ NotificationController imported successfully")
    except Exception as e:
        print(f"   ❌ NotificationController import failed: {str(e)}")
        return False
    
    try:
        print("9. Testing NotificationController instantiation...")
        controller = NotificationController()
        print("   ✅ NotificationController instantiated successfully")
    except Exception as e:
        print(f"   ❌ NotificationController instantiation failed: {str(e)}")
        return False
    
    print("\n✅ All imports and instantiations successful!")
    return True

if __name__ == "__main__":
    success = test_imports()
    if success:
        print("\n🎉 No import issues found!")
    else:
        print("\n❌ Import issues detected!")
