from masonite.notification import Notification
from masonite.mail import Mailable
from masonite.queues import Queueable


class SecurityAlert(Notification, Mailable, Queueable):
    """Security alert notification for suspicious activities and security events"""

    def __init__(self, alert_type, message, details=None, severity="medium"):
        self.alert_type = alert_type
        self.message = message
        self.details = details or {}
        self.severity = severity

    def to_mail(self, notifiable):
        """Send security alert via email"""
        subject = f"🔒 Security Alert: {self.alert_type}"

        # Customize subject based on severity
        if self.severity == "critical":
            subject = f"🚨 CRITICAL Security Alert: {self.alert_type}"
        elif self.severity == "high":
            subject = f"⚠️ HIGH Security Alert: {self.alert_type}"

        return (
            self.to(notifiable.email)
            .subject(subject)
            .from_("<EMAIL>")
            .text(self._build_email_content(notifiable))
        )

    def to_database(self, notifiable):
        """Store security alert in database notifications"""
        return {
            'type': 'security_alert',
            'alert_type': self.alert_type,
            'message': self.message,
            'severity': self.severity,
            'details': self.details,
            'action_url': '/security/dashboard',
            'icon': self._get_severity_icon()
        }

    def to_vonage(self, notifiable):
        """Send security alert via SMS (for critical alerts only)"""
        if self.severity in ['critical', 'high']:
            from masonite.notification.components import Sms
            return Sms().text(f"SECURITY ALERT: {self.message}. Check your email for details.")
        return None

    def via(self, notifiable):
        """Determine notification channels based on severity"""
        channels = ["database", "mail"]

        # Add SMS for critical and high severity alerts
        if self.severity in ['critical', 'high']:
            channels.append("vonage")

        return channels

    def _build_email_content(self, notifiable):
        """Build email content for security alert"""
        content = f"""
Dear {getattr(notifiable, 'name', 'User')},

We detected a security event that requires your attention:

Alert Type: {self.alert_type}
Severity: {self.severity.upper()}
Message: {self.message}

"""

        if self.details:
            content += "Additional Details:\n"
            for key, value in self.details.items():
                content += f"- {key.replace('_', ' ').title()}: {value}\n"

        content += """

What should you do?
1. Review your recent account activity
2. Change your password if you suspect unauthorized access
3. Enable two-factor authentication if not already enabled
4. Contact support if you need assistance

You can view more details in your security dashboard: https://secureapp.com/security/dashboard

Best regards,
SecureApp Security Team

---
This is an automated security notification. Please do not reply to this email.
"""

        return content

    def _get_severity_icon(self):
        """Get icon based on severity level"""
        icons = {
            'low': '🔵',
            'medium': '🟡',
            'high': '🟠',
            'critical': '🔴'
        }
        return icons.get(self.severity, '🔵')
