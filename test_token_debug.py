#!/usr/bin/env python3
"""
Debug token storage and retrieval
"""

import requests
import json
import time
import random
import string

BASE_URL = "http://localhost:8001"
API_BASE = f"{BASE_URL}/api"

def generate_test_email():
    """Generate unique test email"""
    timestamp = int(time.time())
    random_str = ''.join(random.choices(string.ascii_lowercase, k=6))
    return f"test-{timestamp}-{random_str}@example.com"

def test_token_flow():
    """Test token generation and validation"""
    try:
        print("🔍 Testing Token Flow...")
        
        # Generate unique email
        email = generate_test_email()
        
        # Register user
        register_data = {
            "email": email,
            "password": "TestPassword123!",
            "password_confirmation": "TestPassword123!",
            "name": "Test User"
        }
        
        print(f"📧 Registering user: {email}")
        response = requests.post(f"{API_BASE}/auth/register", json=register_data, timeout=10)
        print(f"Registration Status: {response.status_code}")
        
        if response.status_code == 201:
            data = response.json()
            token = data.get('token')
            user_id = data.get('user', {}).get('id')
            
            print(f"✅ Registration successful!")
            print(f"   User ID: {user_id}")
            print(f"   Token: {token}")
            print(f"   Token Length: {len(token) if token else 0}")
            
            # Test different endpoints with the same token
            headers = {'Authorization': f'Bearer {token}'}
            
            endpoints_to_test = [
                '/auth/profile',
                '/security/account-status',
                '/notifications',
                '/queue/status'
            ]
            
            for endpoint in endpoints_to_test:
                try:
                    print(f"\n🧪 Testing {endpoint}...")
                    response = requests.get(f"{API_BASE}{endpoint}", headers=headers, timeout=10)
                    print(f"   Status: {response.status_code}")
                    
                    if response.status_code == 200:
                        print(f"   ✅ Success")
                    elif response.status_code == 401:
                        error_data = response.json()
                        print(f"   ❌ Auth Error: {error_data.get('error', {}).get('message', 'Unknown')}")
                    else:
                        print(f"   ⚠️ Other Error: {response.text[:100]}")
                        
                except Exception as e:
                    print(f"   ❌ Request Error: {str(e)}")
            
            # Test with a different token format to see if it's a token format issue
            print(f"\n🧪 Testing with modified token...")
            modified_headers = {'Authorization': f'Bearer {token}X'}  # Add extra character
            response = requests.get(f"{API_BASE}/auth/profile", headers=modified_headers, timeout=10)
            print(f"   Modified Token Status: {response.status_code}")
            
            # Test without Bearer prefix
            print(f"\n🧪 Testing without Bearer prefix...")
            no_bearer_headers = {'Authorization': token}
            response = requests.get(f"{API_BASE}/auth/profile", headers=no_bearer_headers, timeout=10)
            print(f"   No Bearer Status: {response.status_code}")
            
            return True
            
        else:
            print(f"❌ Registration failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Token test error: {str(e)}")
        return False

def main():
    """Run token debugging"""
    print("🔍 Token Flow Debugging")
    print(f"📍 Testing against: {BASE_URL}")
    
    success = test_token_flow()
    
    if success:
        print("\n✅ Token flow test completed!")
    else:
        print("\n❌ Token flow test failed!")

if __name__ == "__main__":
    main()
