#!/usr/bin/env python3
"""
Debug 500 errors in notification and queue systems
"""

import requests
import json
import time
import random
import string

BASE_URL = "http://localhost:8001"
API_BASE = f"{BASE_URL}/api"

def generate_test_email():
    """Generate unique test email"""
    timestamp = int(time.time())
    random_str = ''.join(random.choices(string.ascii_lowercase, k=6))
    return f"test-{timestamp}-{random_str}@example.com"

def get_auth_token():
    """Get authentication token"""
    try:
        email = generate_test_email()
        register_data = {
            "email": email,
            "password": "TestPassword123!",
            "password_confirmation": "TestPassword123!",
            "name": "Test User"
        }
        
        response = requests.post(f"{API_BASE}/auth/register", json=register_data, timeout=10)
        if response.status_code == 201:
            data = response.json()
            return data.get('token')
        else:
            print(f"Registration failed: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"Registration error: {str(e)}")
        return None

def test_endpoint_detailed(endpoint, method='GET', token=None, data=None):
    """Test endpoint and get detailed error info"""
    try:
        headers = {}
        if token:
            headers['Authorization'] = f'Bearer {token}'
        
        print(f"\n🔍 Testing {method} {endpoint}")
        
        if method == 'GET':
            response = requests.get(f"{API_BASE}{endpoint}", headers=headers, timeout=10)
        else:
            response = requests.post(f"{API_BASE}{endpoint}", json=data or {}, headers=headers, timeout=10)
        
        print(f"Status: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        
        if response.status_code == 500:
            print("🚨 500 ERROR DETAILS:")
            print(f"Content-Type: {response.headers.get('Content-Type', 'Unknown')}")
            
            # Try to get JSON error
            try:
                error_data = response.json()
                print(f"JSON Error: {json.dumps(error_data, indent=2)}")
            except:
                # If not JSON, get text
                error_text = response.text
                print(f"Text Error (first 500 chars): {error_text[:500]}")
                
                # Look for specific error patterns
                if "TypeError" in error_text:
                    print("🔍 Found TypeError in response")
                if "AttributeError" in error_text:
                    print("🔍 Found AttributeError in response")
                if "ImportError" in error_text:
                    print("🔍 Found ImportError in response")
                if "NameError" in error_text:
                    print("🔍 Found NameError in response")
        else:
            try:
                response_data = response.json()
                print(f"Response: {json.dumps(response_data, indent=2)}")
            except:
                print(f"Response Text: {response.text}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ Request Error: {str(e)}")
        return False

def main():
    """Debug 500 errors"""
    print("🔍 Debugging 500 Errors")
    
    # Get auth token
    token = get_auth_token()
    if not token:
        print("❌ Cannot get auth token")
        return
    
    print(f"✅ Got auth token: {token[:20]}...")
    
    # Test problematic endpoints
    print("\n" + "="*60)
    print("TESTING NOTIFICATION ENDPOINTS")
    print("="*60)
    
    # Test notification endpoints
    test_endpoint_detailed('/notifications', 'GET', token)
    test_endpoint_detailed('/notifications/test', 'POST', token, {
        'type': 'security_alert',
        'message': 'Test notification'
    })
    
    print("\n" + "="*60)
    print("TESTING QUEUE ENDPOINTS")
    print("="*60)
    
    # Test queue endpoints
    queue_endpoints = [
        ('/queue/status', 'GET', None),
        ('/queue/stats', 'GET', None),
        ('/queue/failed-jobs', 'GET', None),
        ('/queue/test-email', 'POST', {'email_type': 'welcome', 'message': 'Test'}),
        ('/queue/test-security-processing', 'POST', None),
        ('/queue/test-cleanup', 'POST', None)
    ]
    
    for endpoint, method, data in queue_endpoints:
        test_endpoint_detailed(endpoint, method, token, data)

if __name__ == "__main__":
    main()
