#!/usr/bin/env python3
"""
Debug notification system specifically
"""

import requests
import json
import time
import random
import string

BASE_URL = "http://localhost:8001"
API_BASE = f"{BASE_URL}/api"

def generate_test_email():
    """Generate unique test email"""
    timestamp = int(time.time())
    random_str = ''.join(random.choices(string.ascii_lowercase, k=6))
    return f"test-{timestamp}-{random_str}@example.com"

def get_auth_token():
    """Get authentication token"""
    try:
        email = generate_test_email()
        register_data = {
            "email": email,
            "password": "TestPassword123!",
            "password_confirmation": "TestPassword123!",
            "name": "Test User"
        }
        
        response = requests.post(f"{API_BASE}/auth/register", json=register_data, timeout=10)
        if response.status_code == 201:
            data = response.json()
            return data.get('token'), data.get('user', {}).get('id')
        else:
            print(f"Registration failed: {response.status_code} - {response.text}")
            return None, None
    except Exception as e:
        print(f"Registration error: {str(e)}")
        return None, None

def test_notification_types(token):
    """Test different notification types"""
    headers = {'Authorization': f'Bearer {token}'}
    
    notification_types = [
        ('security_alert', {'type': 'security_alert', 'message': 'Test security alert'}),
        ('account_change', {'type': 'account_change', 'message': 'Test account change'}),
        ('otp', {'type': 'otp', 'message': 'Test OTP notification'})
    ]
    
    for notification_type, data in notification_types:
        try:
            print(f"\n🧪 Testing {notification_type} notification...")
            print(f"   Data: {json.dumps(data, indent=2)}")
            
            response = requests.post(f"{API_BASE}/notifications/test", json=data, headers=headers, timeout=10)
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ Success: {result.get('message', 'No message')}")
            else:
                error_data = response.json()
                print(f"   ❌ Error: {error_data.get('error', {}).get('message', 'Unknown error')}")
                
                # Check if it's a validation error
                if response.status_code == 400:
                    details = error_data.get('error', {}).get('details', {})
                    if details:
                        print(f"   Validation Details: {json.dumps(details, indent=2)}")
                        
        except Exception as e:
            print(f"   ❌ Request Error: {str(e)}")

def test_notification_validation(token):
    """Test notification validation"""
    headers = {'Authorization': f'Bearer {token}'}
    
    print(f"\n🔍 Testing Notification Validation...")
    
    # Test invalid notification type
    invalid_data = {'type': 'invalid_type', 'message': 'Test message'}
    print(f"\n   Testing invalid type: {json.dumps(invalid_data)}")
    
    try:
        response = requests.post(f"{API_BASE}/notifications/test", json=invalid_data, headers=headers, timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code != 200:
            error_data = response.json()
            print(f"   Error: {json.dumps(error_data, indent=2)}")
    except Exception as e:
        print(f"   Request Error: {str(e)}")
    
    # Test missing type
    missing_type_data = {'message': 'Test message'}
    print(f"\n   Testing missing type: {json.dumps(missing_type_data)}")
    
    try:
        response = requests.post(f"{API_BASE}/notifications/test", json=missing_type_data, headers=headers, timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code != 200:
            error_data = response.json()
            print(f"   Error: {json.dumps(error_data, indent=2)}")
    except Exception as e:
        print(f"   Request Error: {str(e)}")

def main():
    """Debug notification system"""
    print("🔍 Notification System Debugging")
    
    # Get auth token
    token, user_id = get_auth_token()
    if not token:
        print("❌ Cannot get auth token")
        return
    
    print(f"✅ Got auth token for user {user_id}: {token[:20]}...")
    
    # Test different notification types
    test_notification_types(token)
    
    # Test validation
    test_notification_validation(token)

if __name__ == "__main__":
    main()
