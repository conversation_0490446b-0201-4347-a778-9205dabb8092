"""CreateSecurityEventsTable Migration."""

from masoniteorm.migrations import Migration


class CreateSecurityEventsTable(Migration):
    def up(self):
        """
        Run the migrations.
        """
        with self.schema.create("security_events") as table:
            table.increments("id")

            # Event identification
            table.string("event_type").index()  # login_failed, login_success, 2fa_failed, etc.
            table.string("event_category").default("security")  # security, authentication, authorization
            table.string("severity").default("info")  # info, warning, error, critical

            # Event details
            table.text("message")
            table.text("event_data").nullable()  # JSON string for additional data

            # User and session tracking
            table.string("user_id").nullable().index()
            table.string("session_id").nullable()
            table.string("ip_address").nullable().index()
            table.text("user_agent").nullable()

            # Location and context
            table.string("endpoint").nullable()
            table.string("method").nullable()
            table.string("referer").nullable()

            # Risk assessment
            table.string("risk_level").default("low")  # low, medium, high, critical
            table.boolean("requires_action").default(False)
            table.boolean("resolved").default(False)
            table.timestamp("resolved_at").nullable()
            table.string("resolved_by").nullable()

            table.timestamps()

    def down(self):
        """
        Revert the migrations.
        """
        self.schema.drop("security_events")
