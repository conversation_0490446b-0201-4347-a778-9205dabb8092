#!/usr/bin/env python3
"""
Debug Datetime Issue in Account Deletion
Test each step to isolate the datetime error
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.models.AccountDeletionRecord import AccountDeletionRecord
from app.models.User import User
from datetime import datetime, timedelta

def test_datetime_operations():
    """Test datetime operations directly"""
    print("🔧 Testing Datetime Operations...")
    
    try:
        # Test 1: Create a simple datetime
        print("1. Testing datetime.now()...")
        now = datetime.now()
        print(f"✅ Current time: {now}")
        
        # Test 2: Create a timedelta
        print("2. Testing timedelta...")
        future = now + timedelta(hours=24)
        print(f"✅ Future time: {future}")
        
        # Test 3: Test AccountDeletionRecord creation
        print("3. Testing AccountDeletionRecord creation...")
        test_preferences = {
            'preservePaymentData': True,
            'preserveTransactionHistory': False,
            'preserveProfileData': True,
            'preserveSecurityLogs': True,
            'customRetentionPeriod': 60,
            'reason': 'Testing datetime operations'
        }
        
        # Create a test record
        deletion_record = AccountDeletionRecord.create_deletion_request(
            user_id=999,
            email="<EMAIL>",
            preferences=test_preferences
        )
        print(f"✅ Deletion record created: {deletion_record.id}")
        
        # Test 4: Test token validation
        print("4. Testing token validation...")
        is_valid = deletion_record.is_confirmation_token_valid()
        print(f"✅ Token is valid: {is_valid}")
        
        # Test 5: Test confirm deletion
        print("5. Testing confirm deletion...")
        deletion_record.confirm_deletion()
        print(f"✅ Deletion confirmed")
        
        # Test 6: Test complete deletion
        print("6. Testing complete deletion...")
        deletion_record.complete_deletion()
        print(f"✅ Deletion completed")
        
        # Clean up
        deletion_record.delete()
        print("✅ Test record cleaned up")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in datetime operations: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the datetime debug test"""
    print("🐛 Debugging Datetime Operations")
    print("=" * 40)
    
    success = test_datetime_operations()
    
    print("\n" + "=" * 40)
    if success:
        print("✅ All datetime operations work correctly!")
    else:
        print("❌ Datetime operations have issues")

if __name__ == "__main__":
    main()
