#!/usr/bin/env python3
"""
Quick test of the three systems with proper error handling
"""

import requests
import json
import time
import random
import string

BASE_URL = "http://localhost:8001"
API_BASE = f"{BASE_URL}/api"

def generate_test_email():
    """Generate unique test email"""
    timestamp = int(time.time())
    random_str = ''.join(random.choices(string.ascii_lowercase, k=6))
    return f"test-{timestamp}-{random_str}@example.com"

def test_registration_and_login():
    """Test user registration and login"""
    try:
        print("🧪 Testing Registration and Login...")
        
        # Generate unique email
        email = generate_test_email()
        
        # Register user
        register_data = {
            "email": email,
            "password": "TestPassword123!",
            "password_confirmation": "TestPassword123!",
            "name": "Test User"
        }
        
        response = requests.post(f"{API_BASE}/auth/register", json=register_data, timeout=10)
        print(f"Registration: {response.status_code}")
        
        if response.status_code == 201:
            data = response.json()
            token = data.get('token')
            user_id = data.get('user', {}).get('id')
            print(f"✅ User registered successfully: {user_id}")
            return token, user_id, email
        else:
            print(f"❌ Registration failed: {response.text}")
            return None, None, None
            
    except Exception as e:
        print(f"❌ Registration error: {str(e)}")
        return None, None, None

def test_security_features(token):
    """Test security features"""
    print("\n🔒 Testing Security Features...")
    
    headers = {'Authorization': f'Bearer {token}'}
    
    endpoints = [
        '/security/events',
        '/security/account-status',
        '/security/dashboard',
        '/security/suspicious-activity',
        '/security/analysis'
    ]
    
    results = {}
    for endpoint in endpoints:
        try:
            response = requests.get(f"{API_BASE}{endpoint}", headers=headers, timeout=10)
            status = "✅ PASS" if response.status_code == 200 else f"❌ FAIL ({response.status_code})"
            print(f"  {endpoint}: {status}")
            results[endpoint] = response.status_code == 200
        except Exception as e:
            print(f"  {endpoint}: ❌ ERROR - {str(e)}")
            results[endpoint] = False
    
    return results

def test_notification_system(token):
    """Test notification system"""
    print("\n📧 Testing Notification System...")
    
    headers = {'Authorization': f'Bearer {token}'}
    
    # Test getting notifications
    try:
        response = requests.get(f"{API_BASE}/notifications", headers=headers, timeout=10)
        status = "✅ PASS" if response.status_code == 200 else f"❌ FAIL ({response.status_code})"
        print(f"  Get Notifications: {status}")
        get_notifications = response.status_code == 200
    except Exception as e:
        print(f"  Get Notifications: ❌ ERROR - {str(e)}")
        get_notifications = False
    
    # Test sending test notification
    try:
        test_data = {
            'type': 'security_alert',
            'message': 'Test notification'
        }
        response = requests.post(f"{API_BASE}/notifications/test", json=test_data, headers=headers, timeout=10)
        status = "✅ PASS" if response.status_code == 200 else f"❌ FAIL ({response.status_code})"
        print(f"  Send Test Notification: {status}")
        send_notification = response.status_code == 200
    except Exception as e:
        print(f"  Send Test Notification: ❌ ERROR - {str(e)}")
        send_notification = False
    
    return {
        'get_notifications': get_notifications,
        'send_notification': send_notification
    }

def test_queue_system(token):
    """Test queue system"""
    print("\n⚙️ Testing Queue System...")
    
    headers = {'Authorization': f'Bearer {token}'}
    
    endpoints = [
        ('/queue/status', 'GET'),
        ('/queue/stats', 'GET'),
        ('/queue/failed-jobs', 'GET'),
        ('/queue/test-email', 'POST'),
        ('/queue/test-security-processing', 'POST'),
        ('/queue/test-cleanup', 'POST')
    ]
    
    results = {}
    for endpoint, method in endpoints:
        try:
            if method == 'GET':
                response = requests.get(f"{API_BASE}{endpoint}", headers=headers, timeout=10)
            else:
                test_data = {'email_type': 'welcome', 'message': 'Test'} if 'email' in endpoint else {}
                response = requests.post(f"{API_BASE}{endpoint}", json=test_data, headers=headers, timeout=10)
            
            status = "✅ PASS" if response.status_code == 200 else f"❌ FAIL ({response.status_code})"
            print(f"  {endpoint}: {status}")
            results[endpoint] = response.status_code == 200
        except Exception as e:
            print(f"  {endpoint}: ❌ ERROR - {str(e)}")
            results[endpoint] = False
    
    return results

def main():
    """Run all tests"""
    print("🚀 Quick System Testing")
    print(f"📍 Testing against: {BASE_URL}")
    
    # Test registration and login
    token, user_id, email = test_registration_and_login()
    
    if not token:
        print("❌ Cannot proceed without authentication token")
        return False
    
    print(f"✅ Authentication successful for: {email}")
    
    # Test all systems
    security_results = test_security_features(token)
    notification_results = test_notification_system(token)
    queue_results = test_queue_system(token)
    
    # Calculate results
    security_passed = sum(security_results.values())
    security_total = len(security_results)
    
    notification_passed = sum(notification_results.values())
    notification_total = len(notification_results)
    
    queue_passed = sum(queue_results.values())
    queue_total = len(queue_results)
    
    total_passed = security_passed + notification_passed + queue_passed
    total_tests = security_total + notification_total + queue_total
    
    # Print summary
    print(f"\n🎯 SUMMARY:")
    print(f"🔒 Security Features: {security_passed}/{security_total} passed")
    print(f"📧 Notification System: {notification_passed}/{notification_total} passed")
    print(f"⚙️ Queue System: {queue_passed}/{queue_total} passed")
    print(f"📊 Overall: {total_passed}/{total_tests} tests passed ({(total_passed/total_tests)*100:.1f}%)")
    
    if total_passed >= total_tests * 0.8:
        print("✅ Systems are working well!")
        return True
    else:
        print("⚠️ Some systems need attention")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
