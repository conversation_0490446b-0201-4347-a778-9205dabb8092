from masonite.queues import Queueable
from app.models.SecurityEvent import SecurityEvent, SecurityEventType
from app.models.User import User
from app.services.NotificationService import NotificationService
from datetime import datetime, timezone, timedelta
import json


class ProcessSecurityEventJob(Queueable):
    """Background job for processing security events and taking automated actions"""

    def __init__(self, event_id, action_type="analyze"):
        """
        Initialize security event processing job

        Args:
            event_id: ID of the security event to process
            action_type: Type of action to take (analyze, notify, block, etc.)
        """
        self.event_id = event_id
        self.action_type = action_type

    def handle(self):
        """Process the security event"""
        try:
            print(f"🔍 Processing security event: {self.event_id} (action: {self.action_type})")

            # Get the security event
            event = SecurityEvent.find(self.event_id)
            if not event:
                print(f"❌ Security event {self.event_id} not found")
                return False

            # Process based on action type
            success = False
            if self.action_type == "analyze":
                success = self._analyze_event(event)
            elif self.action_type == "notify":
                success = self._notify_about_event(event)
            elif self.action_type == "block":
                success = self._block_suspicious_activity(event)
            elif self.action_type == "cleanup":
                success = self._cleanup_related_events(event)
            else:
                success = self._default_processing(event)

            if success:
                print(f"✅ Security event processed successfully: {self.event_id}")
            else:
                print(f"❌ Failed to process security event: {self.event_id}")

            return success

        except Exception as e:
            print(f"❌ Security event processing error: {str(e)}")
            return False

    def _analyze_event(self, event):
        """Analyze security event for patterns and risks"""
        try:
            print(f"🔍 Analyzing security event: {event.event_type}")

            # Check for patterns based on event type
            if event.event_type == SecurityEventType.LOGIN_FAILED.value:
                return self._analyze_failed_login(event)
            elif event.event_type == SecurityEventType.SUSPICIOUS_ACTIVITY.value:
                return self._analyze_suspicious_activity(event)
            elif event.event_type == SecurityEventType.RATE_LIMIT_EXCEEDED.value:
                return self._analyze_rate_limit_violation(event)
            else:
                return self._analyze_generic_event(event)

        except Exception as e:
            print(f"❌ Event analysis error: {str(e)}")
            return False

    def _analyze_failed_login(self, event):
        """Analyze failed login patterns"""
        try:
            # Check for multiple failed logins from same IP
            if event.ip_address:
                recent_failures = SecurityEvent.where('event_type', SecurityEventType.LOGIN_FAILED.value)\
                                               .where('ip_address', event.ip_address)\
                                               .where('created_at', '>', datetime.now(timezone.utc) - timedelta(hours=1))\
                                               .count()

                if recent_failures > 10:
                    # Create high-risk event for IP blocking
                    SecurityEvent.log_suspicious_activity(
                        description=f"Multiple failed logins from IP {event.ip_address} ({recent_failures} attempts)",
                        ip_address=event.ip_address,
                        event_data={'failed_login_count': recent_failures, 'analysis_result': 'potential_brute_force'}
                    )

                    print(f"⚠️ Potential brute force detected from IP: {event.ip_address}")

            # Check for failed logins across multiple accounts
            if event.user_id:
                user_failures = SecurityEvent.where('event_type', SecurityEventType.LOGIN_FAILED.value)\
                                            .where('user_id', event.user_id)\
                                            .where('created_at', '>', datetime.now(timezone.utc) - timedelta(hours=24))\
                                            .count()

                if user_failures > 5:
                    # Trigger account lockout check
                    user = User.find(event.user_id)
                    if user and not user.is_locked():
                        print(f"⚠️ User {event.user_id} may need account lockout review")

            return True

        except Exception as e:
            print(f"❌ Failed login analysis error: {str(e)}")
            return False

    def _analyze_suspicious_activity(self, event):
        """Analyze suspicious activity patterns"""
        try:
            event_data = event.get_event_data_dict()
            indicators = event_data.get('indicators', [])

            # Escalate if multiple indicators present
            if len(indicators) > 2:
                event.risk_level = 'critical'
                event.requires_action = True
                event.save()

                print(f"🚨 Escalated suspicious activity to critical: {event.id}")

                # Notify security team
                if event.user_id:
                    notification_service = NotificationService()
                    notification_service.send_suspicious_activity_alert(
                        user_id=event.user_id,
                        activity_description=f"Critical suspicious activity detected: {', '.join(indicators)}",
                        details=event_data
                    )

            return True

        except Exception as e:
            print(f"❌ Suspicious activity analysis error: {str(e)}")
            return False

    def _analyze_rate_limit_violation(self, event):
        """Analyze rate limit violations"""
        try:
            # Check for excessive rate limit violations from same IP
            if event.ip_address:
                recent_violations = SecurityEvent.where('event_type', SecurityEventType.RATE_LIMIT_EXCEEDED.value)\
                                                 .where('ip_address', event.ip_address)\
                                                 .where('created_at', '>', datetime.now(timezone.utc) - timedelta(hours=1))\
                                                 .count()

                if recent_violations > 50:
                    # Create blocking recommendation
                    SecurityEvent.log_suspicious_activity(
                        description=f"Excessive rate limit violations from IP {event.ip_address} ({recent_violations} violations)",
                        ip_address=event.ip_address,
                        event_data={'violation_count': recent_violations, 'recommendation': 'ip_block'}
                    )

                    print(f"🚫 IP blocking recommended for: {event.ip_address}")

            return True

        except Exception as e:
            print(f"❌ Rate limit analysis error: {str(e)}")
            return False

    def _analyze_generic_event(self, event):
        """Analyze generic security events"""
        try:
            # Basic analysis for other event types
            if event.risk_level in ['high', 'critical'] and not event.requires_action:
                event.requires_action = True
                event.save()
                print(f"⚠️ Marked high-risk event for action: {event.id}")

            return True

        except Exception as e:
            print(f"❌ Generic event analysis error: {str(e)}")
            return False

    def _notify_about_event(self, event):
        """Send notifications about security event"""
        try:
            if event.user_id and event.risk_level in ['high', 'critical']:
                notification_service = NotificationService()

                if event.event_type == SecurityEventType.ACCOUNT_LOCKED.value:
                    notification_service.send_account_locked_alert(
                        user_id=event.user_id,
                        reason=event.message
                    )
                elif event.event_type == SecurityEventType.SUSPICIOUS_ACTIVITY.value:
                    notification_service.send_suspicious_activity_alert(
                        user_id=event.user_id,
                        activity_description=event.message,
                        details=event.get_event_data_dict()
                    )
                else:
                    notification_service.send_security_alert(
                        user_id=event.user_id,
                        alert_type=event.event_type,
                        message=event.message,
                        severity=event.severity
                    )

                print(f"📧 Notification sent for security event: {event.id}")

            return True

        except Exception as e:
            print(f"❌ Event notification error: {str(e)}")
            return False

    def _block_suspicious_activity(self, event):
        """Take blocking actions for suspicious activity"""
        try:
            # This would implement actual blocking logic
            # For now, just log the action
            print(f"🚫 Blocking action would be taken for event: {event.id}")

            # Mark event as resolved
            event.mark_resolved("automated_system")

            return True

        except Exception as e:
            print(f"❌ Blocking action error: {str(e)}")
            return False

    def _cleanup_related_events(self, event):
        """Clean up related security events"""
        try:
            # Clean up old resolved events of the same type
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=30)
            old_events = SecurityEvent.where('event_type', event.event_type)\
                                     .where('resolved', True)\
                                     .where('created_at', '<', cutoff_date)\
                                     .get()

            count = 0
            for old_event in old_events:
                old_event.delete()
                count += 1

            print(f"🧹 Cleaned up {count} old {event.event_type} events")
            return True

        except Exception as e:
            print(f"❌ Event cleanup error: {str(e)}")
            return False

    def _default_processing(self, event):
        """Default processing for security events"""
        try:
            # Basic processing - just mark as processed
            print(f"📝 Default processing for event: {event.event_type}")
            return True

        except Exception as e:
            print(f"❌ Default processing error: {str(e)}")
            return False
