"""Notification Service for managing all types of notifications"""

# from masonite.facades import Notification
from app.notifications.SecurityAlert import <PERSON><PERSON><PERSON>t
from app.notifications.AccountChange import Account<PERSON>hange
from app.notifications.OTPNotification import OTPNotification
from app.models.User import User
from app.models.SecurityEvent import SecurityEvent, SecurityEventType
from datetime import datetime, timezone


class NotificationService:
    """Service for managing and sending notifications"""

    def __init__(self):
        pass  # Use database notifications directly

    def send_security_alert(self, user_id, alert_type, message, details=None, severity="medium"):
        """Send security alert notification"""
        try:
            user = User.find(user_id)
            if not user:
                print(f"❌ User {user_id} not found for security alert")
                return False

            # Create and send notification
            alert = SecurityAlert(alert_type, message, details, severity)
            # Notification.send(user, alert)

            # For now, simulate successful sending
            print(f"📧 Simulated sending security alert to user {user_id}: {alert_type}")

            # Store notification in database
            from app.models.Notification import Notification as NotificationModel
            NotificationModel.create({
                'user_id': user_id,
                'type': 'security_alert',
                'title': alert_type,
                'message': message,
                'data': details,
                'read': False
            })

            # Log the notification event
            SecurityEvent.log_event(
                "notification_sent",
                f"Security alert sent to user {user_id}: {alert_type}",
                user_id=user_id,
                event_data={
                    'notification_type': 'security_alert',
                    'alert_type': alert_type,
                    'severity': severity
                },
                severity="info"
            )

            print(f"✅ Security alert sent to user {user_id}: {alert_type}")
            return True

        except Exception as e:
            print(f"❌ Failed to send security alert: {str(e)}")
            return False

    def send_account_change_notification(self, user_id, change_type, description, 
                                       details=None, requires_action=False):
        """Send account change notification"""
        try:
            user = User.find(user_id)
            if not user:
                print(f"❌ User {user_id} not found for account change notification")
                return False

            # Create and send notification
            notification = AccountChange(change_type, description, details, requires_action)
            # Notification.send(user, notification)

            # For now, simulate successful sending
            print(f"📧 Simulated sending account change notification to user {user_id}: {change_type}")

            # Store notification in database
            from app.models.Notification import Notification as NotificationModel
            NotificationModel.create({
                'user_id': user_id,
                'type': 'account_change',
                'title': change_type,
                'message': description,
                'data': details,
                'read': False
            })

            # Log the notification event
            SecurityEvent.log_event(
                "notification_sent",
                f"Account change notification sent to user {user_id}: {change_type}",
                user_id=user_id,
                event_data={
                    'notification_type': 'account_change',
                    'change_type': change_type,
                    'requires_action': requires_action
                },
                severity="info"
            )

            print(f"✅ Account change notification sent to user {user_id}: {change_type}")
            return True

        except Exception as e:
            print(f"❌ Failed to send account change notification: {str(e)}")
            return False

    def send_otp_notification(self, user_id, otp_code, otp_type="login", expires_in_minutes=10):
        """Send OTP notification"""
        try:
            user = User.find(user_id)
            if not user:
                print(f"❌ User {user_id} not found for OTP notification")
                return False

            # Create and send notification
            otp_notification = OTPNotification(otp_code, otp_type, expires_in_minutes)
            # Notification.send(user, otp_notification)

            # For now, simulate successful sending
            print(f"📧 Simulated sending OTP notification to user {user_id}: {otp_type}")

            # Store notification in database
            from app.models.Notification import Notification as NotificationModel
            NotificationModel.create({
                'user_id': user_id,
                'type': 'otp',
                'title': f'OTP Code - {otp_type}',
                'message': f'Your OTP code is: {otp_code}',
                'data': {'otp_code': otp_code, 'expires_in_minutes': expires_in_minutes},
                'read': False
            })

            # Log the OTP event
            SecurityEvent.log_otp_event(
                SecurityEventType.OTP_SENT.value,
                user.email,
                user_id=user_id
            )

            print(f"✅ OTP notification sent to user {user_id}: {otp_type}")
            return True

        except Exception as e:
            print(f"❌ Failed to send OTP notification: {str(e)}")
            return False

    def send_login_alert(self, user_id, ip_address=None, user_agent=None, location=None):
        """Send login alert notification"""
        try:
            details = {}
            if ip_address:
                details['ip_address'] = ip_address
            if user_agent:
                details['user_agent'] = user_agent
            if location:
                details['location'] = location
            
            details['login_time'] = datetime.now(timezone.utc).isoformat()

            return self.send_security_alert(
                user_id=user_id,
                alert_type="New Login Detected",
                message="A new login to your account was detected",
                details=details,
                severity="low"
            )

        except Exception as e:
            print(f"❌ Failed to send login alert: {str(e)}")
            return False

    def send_suspicious_activity_alert(self, user_id, activity_description, details=None):
        """Send suspicious activity alert"""
        try:
            return self.send_security_alert(
                user_id=user_id,
                alert_type="Suspicious Activity Detected",
                message=f"Suspicious activity detected: {activity_description}",
                details=details,
                severity="high"
            )

        except Exception as e:
            print(f"❌ Failed to send suspicious activity alert: {str(e)}")
            return False

    def send_account_locked_alert(self, user_id, reason, locked_until=None):
        """Send account locked alert"""
        try:
            details = {'reason': reason}
            if locked_until:
                details['locked_until'] = str(locked_until)

            return self.send_security_alert(
                user_id=user_id,
                alert_type="Account Locked",
                message=f"Your account has been locked: {reason}",
                details=details,
                severity="critical"
            )

        except Exception as e:
            print(f"❌ Failed to send account locked alert: {str(e)}")
            return False

    def send_password_changed_notification(self, user_id, ip_address=None):
        """Send password changed notification"""
        try:
            details = {}
            if ip_address:
                details['ip_address'] = ip_address
            details['changed_at'] = datetime.now(timezone.utc).isoformat()

            return self.send_account_change_notification(
                user_id=user_id,
                change_type="password_changed",
                description="Your password has been successfully changed",
                details=details,
                requires_action=False
            )

        except Exception as e:
            print(f"❌ Failed to send password changed notification: {str(e)}")
            return False

    def send_email_verification_notification(self, user_id, verification_token):
        """Send email verification notification"""
        try:
            details = {
                'verification_token': verification_token,
                'verification_url': f"https://secureapp.com/verify-email?token={verification_token}"
            }

            return self.send_account_change_notification(
                user_id=user_id,
                change_type="email_verification",
                description="Please verify your email address",
                details=details,
                requires_action=True
            )

        except Exception as e:
            print(f"❌ Failed to send email verification notification: {str(e)}")
            return False

    def send_two_factor_enabled_notification(self, user_id):
        """Send two-factor authentication enabled notification"""
        try:
            return self.send_account_change_notification(
                user_id=user_id,
                change_type="two_factor_enabled",
                description="Two-factor authentication has been enabled on your account",
                details={'enabled_at': datetime.now(timezone.utc).isoformat()},
                requires_action=False
            )

        except Exception as e:
            print(f"❌ Failed to send 2FA enabled notification: {str(e)}")
            return False

    def send_account_deletion_notification(self, user_id, deletion_date):
        """Send account deletion notification"""
        try:
            details = {
                'deletion_date': str(deletion_date),
                'cancellation_url': 'https://secureapp.com/cancel-deletion'
            }

            return self.send_account_change_notification(
                user_id=user_id,
                change_type="account_deletion_scheduled",
                description="Your account deletion has been scheduled",
                details=details,
                requires_action=True
            )

        except Exception as e:
            print(f"❌ Failed to send account deletion notification: {str(e)}")
            return False

    def get_user_notifications(self, user_id, limit=50):
        """Get notifications for a user"""
        try:
            from app.models.Notification import Notification as NotificationModel
            notifications = NotificationModel.get_user_notifications(user_id, limit)
            return [notification.to_dict() for notification in notifications]

        except Exception as e:
            print(f"❌ Failed to get user notifications: {str(e)}")
            return []

    def mark_notification_as_read(self, notification_id, user_id):
        """Mark notification as read"""
        try:
            from app.models.Notification import Notification as NotificationModel
            notification = NotificationModel.where('id', notification_id).where('user_id', user_id).first()
            if notification:
                notification.mark_as_read()
                return True
            return False

        except Exception as e:
            print(f"❌ Failed to mark notification as read: {str(e)}")
            return False

    def send_bulk_notification(self, user_ids, notification_type, **kwargs):
        """Send notification to multiple users"""
        try:
            success_count = 0
            
            for user_id in user_ids:
                if notification_type == "security_alert":
                    success = self.send_security_alert(user_id, **kwargs)
                elif notification_type == "account_change":
                    success = self.send_account_change_notification(user_id, **kwargs)
                elif notification_type == "otp":
                    success = self.send_otp_notification(user_id, **kwargs)
                else:
                    print(f"❌ Unknown notification type: {notification_type}")
                    continue
                
                if success:
                    success_count += 1

            print(f"✅ Bulk notification sent to {success_count}/{len(user_ids)} users")
            return success_count

        except Exception as e:
            print(f"❌ Failed to send bulk notification: {str(e)}")
            return 0
