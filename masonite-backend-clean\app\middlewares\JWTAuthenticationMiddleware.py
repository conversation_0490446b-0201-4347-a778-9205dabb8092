from masonite.middleware import Middleware
from masonite.authentication import Auth
from app.models.User import User


class JWTAuthenticationMiddleware(Middleware):
    """JWT Authentication Middleware for API routes."""
    
    def before(self, request, response):
        """Authenticate API requests using JWT tokens."""
        # Get token from Authorization header or token parameter
        auth_header = request.header('Authorization') or ''
        token = None
        
        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header[7:]  # Remove 'Bearer ' prefix
        elif request.input('token'):
            token = request.input('token')
        
        if not token:
            return response.json({
                'error': {
                    'statusCode': 401,
                    'name': 'UnauthorizedError',
                    'message': 'No authentication token provided'
                }
            }, 401)        # Find user by API token (ensure token is not None/empty)
        user = User.where('api_token', token).first()
        if not user or not user.api_token:
            return response.json({
                'error': {
                    'statusCode': 401,
                    'name': 'UnauthorizedError',
                    'message': 'Invalid authentication token'
                }
            }, 401)
        
        # Set the authenticated user
        request.set_user(user)
        return request

    def after(self, request, response):
        return request
