"""OTP Controller for handling one-time password operations"""

from masonite.controllers import Controller
from masonite.request import Request
from masonite.response import Response
from masonite.validation import Validator
from masonite.mail import Mail
from masonite.authentication import Auth
from app.services.OTPService import OTPService


class O<PERSON><PERSON><PERSON>roller(Controller):
    """Handle OTP operations including sending, verification, and login"""

    def __init__(self, mail: Mail):
        """Initialize OTP service with mail dependency injection"""
        self.otp_service = OTPService(mail)

    def send_otp(self, request: Request, response: Response, validate: Validator):
        """
        POST /api/otp/send
        Send OTP via email or SMS
        """
        # Validate request
        errors = validate.validate({
            'type': 'required|string|in:login,verification,2fa',
            'email': 'string|email',
            'phone': 'string',
        })

        if errors:
            return response.json({
                'error': {
                    'statusCode': 400,
                    'name': 'ValidationError',
                    'message': 'Invalid request data',
                    'details': errors
                }
            }, 400)

        try:
            email = request.input('email')
            phone = request.input('phone')
            otp_type = request.input('type')

            if not email and not phone:
                return response.json({
                    'error': {
                        'statusCode': 400,
                        'name': 'BadRequestError',
                        'message': 'Either email or phone is required'
                    }
                }, 400)

            # Use email if provided, otherwise phone
            identifier = email if email else phone
            delivery_method = 'email' if email else 'sms'

            print(f"🔍 OTP Send Request: {identifier} ({otp_type})")

            # Send OTP
            result = self.otp_service.send_otp(
                identifier=identifier,
                otp_type=otp_type,
                delivery_method=delivery_method,
                ip_address=request.ip(),
                user_agent=request.header('User-Agent')
            )

            return response.json(result, 200)

        except Exception as e:
            print(f"❌ OTP Send Error: {str(e)}")
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': str(e)
                }
            }, 500)

    def send_email_otp(self, request: Request, response: Response, validate: Validator):
        """
        POST /api/otp/send-email
        Send OTP via email specifically
        """
        # Validate request
        errors = validate.validate({
            'email': 'required|email',
            'type': 'required|string|in:login,verification,2fa'
        })

        if errors:
            return response.json({
                'error': {
                    'statusCode': 400,
                    'name': 'ValidationError',
                    'message': 'Invalid request data',
                    'details': errors
                }
            }, 400)

        try:
            email = request.input('email')
            otp_type = request.input('type')

            print(f"📧 Email OTP Request: {email} ({otp_type})")

            # Send OTP via email
            result = self.otp_service.send_otp(
                identifier=email,
                otp_type=otp_type,
                delivery_method='email',
                ip_address=request.ip(),
                user_agent=request.header('User-Agent')
            )

            return response.json({
                'message': 'OTP sent successfully'
            }, 200)

        except Exception as e:
            print(f"❌ Email OTP Send Error: {str(e)}")
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': str(e)
                }
            }, 500)

    def send_sms_otp(self, request: Request, response: Response, validate: Validator):
        """
        POST /api/otp/send-sms
        Send OTP via SMS specifically
        """
        # Validate request
        errors = validate.validate({
            'phone': 'required|string',
            'type': 'required|string|in:login,verification,2fa'
        })

        if errors:
            return response.json({
                'error': {
                    'statusCode': 400,
                    'name': 'ValidationError',
                    'message': 'Invalid request data',
                    'details': errors
                }
            }, 400)

        try:
            phone = request.input('phone')
            otp_type = request.input('type')

            print(f"📱 SMS OTP Request: {phone} ({otp_type})")

            # Send OTP via SMS
            result = self.otp_service.send_otp(
                identifier=phone,
                otp_type=otp_type,
                delivery_method='sms',
                ip_address=request.ip(),
                user_agent=request.header('User-Agent')
            )

            return response.json({
                'message': 'OTP sent successfully'
            }, 200)

        except Exception as e:
            print(f"❌ SMS OTP Send Error: {str(e)}")
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': str(e)
                }
            }, 500)

    def verify_otp(self, request: Request, response: Response, validate: Validator):
        """
        POST /api/otp/verify
        Verify OTP code
        """
        # Validate request
        errors = validate.validate({
            'identifier': 'required|string',
            'code': 'required|string',
            'type': 'required|string|in:login,verification,2fa'
        })

        if errors:
            return response.json({
                'error': {
                    'statusCode': 400,
                    'name': 'ValidationError',
                    'message': 'Invalid request data',
                    'details': errors
                }
            }, 400)

        try:
            identifier = request.input('identifier')
            code = request.input('code')
            otp_type = request.input('type')

            print(f"🔍 OTP Verification: {identifier} ({otp_type})")

            # Verify OTP
            result = self.otp_service.verify_otp(identifier, code, otp_type)

            return response.json(result, 200)

        except Exception as e:
            print(f"❌ OTP Verification Error: {str(e)}")
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'OTP verification failed'
                }
            }, 500)

    def login_with_otp(self, request: Request, response: Response, validate: Validator, auth: Auth):
        """
        POST /api/otp/login
        Login with OTP
        """
        # Validate request
        errors = validate.validate({
            'identifier': 'required|string',
            'code': 'required|string'
        })

        if errors:
            return response.json({
                'error': {
                    'statusCode': 400,
                    'name': 'ValidationError',
                    'message': 'Invalid request data',
                    'details': errors
                }
            }, 400)

        try:
            identifier = request.input('identifier')
            code = request.input('code')

            print(f"🔑 OTP Login: {identifier}")

            # Login with OTP
            result = self.otp_service.login_with_otp(identifier, code)

            if result['success']:
                # Generate JWT token using Masonite's auth system
                user_data = result['user']
                token = auth.guard('api').login(user_data['id'])

                return response.json({
                    'token': token,
                    'user': user_data,
                    'message': result['message']
                }, 200)
            else:
                return response.json({
                    'error': {
                        'statusCode': 401,
                        'name': 'UnauthorizedError',
                        'message': 'OTP login failed'
                    }
                }, 401)

        except Exception as e:
            print(f"❌ OTP Login Error: {str(e)}")
            return response.json({
                'error': {
                    'statusCode': 401,
                    'name': 'UnauthorizedError',
                    'message': str(e)
                }
            }, 401)

    def get_otp_status(self, request: Request, response: Response, validate: Validator):
        """
        GET /api/otp/status
        Get OTP status for identifier
        """
        # Validate request
        errors = validate.validate({
            'identifier': 'required|string',
            'type': 'string|in:login,verification,2fa'
        })

        if errors:
            return response.json({
                'error': {
                    'statusCode': 400,
                    'name': 'ValidationError',
                    'message': 'Invalid request data',
                    'details': errors
                }
            }, 400)

        try:
            identifier = request.input('identifier')
            otp_type = request.input('type', 'login')

            # Get OTP status
            result = self.otp_service.get_otp_status(identifier, otp_type)

            return response.json(result, 200)

        except Exception as e:
            print(f"❌ OTP Status Error: {str(e)}")
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Failed to get OTP status'
                }
            }, 500)

    def cleanup_otps(self, request: Request, response: Response):
        """
        POST /api/otp/cleanup
        Clean up expired and used OTPs (admin endpoint)
        """
        try:
            print("🧹 Admin OTP cleanup request received")

            # Cleanup expired OTPs
            result = self.otp_service.cleanup_expired_otps()

            return response.json(result, 200)

        except Exception as e:
            print(f"❌ OTP Cleanup Error: {str(e)}")
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Failed to cleanup OTPs'
                }
            }, 500)
