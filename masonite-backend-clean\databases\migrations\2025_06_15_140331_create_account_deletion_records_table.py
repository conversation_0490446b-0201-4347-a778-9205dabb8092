"""CreateAccountDeletionRecordsTable Migration."""

from masoniteorm.migrations import Migration


class CreateAccountDeletionRecordsTable(Migration):
    def up(self):
        """
        Run the migrations.
        """
        with self.schema.create("account_deletion_records") as table:
            table.increments("id")

            # User identification
            table.string("user_id").nullable()  # Original user ID before deletion
            table.string("email").index()  # Email for identification

            # Deletion request details
            table.string("deletion_id").unique()  # Unique deletion request ID
            table.string("deletion_status").default("pending_confirmation")  # pending_confirmation, confirmed, completed, cancelled
            table.string("confirmation_token").nullable()  # Token for email confirmation
            table.datetime("confirmation_token_expires").nullable()

            # Data preservation preferences
            table.boolean("preserve_payment_data").default(False)
            table.boolean("preserve_transaction_history").default(False)
            table.boolean("preserve_profile_data").default(False)
            table.boolean("preserve_security_logs").default(False)
            table.integer("custom_retention_period").default(30)  # Days
            table.text("reason").nullable()  # User's reason for deletion

            # Preserved data (JSON fields)
            table.json("preserved_user_data").nullable()
            table.json("preserved_payment_data").nullable()
            table.json("preserved_transaction_data").nullable()
            table.json("preserved_security_data").nullable()

            # Deletion tracking
            table.datetime("requested_at")
            table.datetime("confirmed_at").nullable()
            table.datetime("completed_at").nullable()
            table.datetime("expires_at").nullable()  # When preserved data expires

            table.timestamps()

    def down(self):
        """
        Revert the migrations.
        """
        self.schema.drop("account_deletion_records")
