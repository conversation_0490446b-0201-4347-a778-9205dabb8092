from masonite.queues import Queueable
from app.models.SecurityEvent import SecurityEvent
from app.models.OTP import OTP
from app.models.User import User
from datetime import datetime, timezone, timedelta


class DataCleanupJob(Queueable):
    """Background job for cleaning up old data and maintaining database health"""

    def __init__(self, cleanup_type="all", days_to_keep=90):
        """
        Initialize data cleanup job

        Args:
            cleanup_type: Type of cleanup (all, security_events, otps, users, etc.)
            days_to_keep: Number of days to keep data (default: 90)
        """
        self.cleanup_type = cleanup_type
        self.days_to_keep = days_to_keep

    def handle(self):
        """Process the data cleanup"""
        try:
            print(f"🧹 Starting data cleanup: {self.cleanup_type} (keeping {self.days_to_keep} days)")

            total_cleaned = 0

            if self.cleanup_type == "all":
                total_cleaned += self._cleanup_security_events()
                total_cleaned += self._cleanup_otps()
                total_cleaned += self._cleanup_inactive_users()
                total_cleaned += self._cleanup_expired_tokens()
            elif self.cleanup_type == "security_events":
                total_cleaned += self._cleanup_security_events()
            elif self.cleanup_type == "otps":
                total_cleaned += self._cleanup_otps()
            elif self.cleanup_type == "users":
                total_cleaned += self._cleanup_inactive_users()
            elif self.cleanup_type == "tokens":
                total_cleaned += self._cleanup_expired_tokens()
            else:
                print(f"❌ Unknown cleanup type: {self.cleanup_type}")
                return False

            print(f"✅ Data cleanup completed: {total_cleaned} records cleaned")

            # Log cleanup completion
            SecurityEvent.log_event(
                "data_cleanup_completed",
                f"Data cleanup completed: {self.cleanup_type}",
                event_data={
                    'cleanup_type': self.cleanup_type,
                    'days_to_keep': self.days_to_keep,
                    'records_cleaned': total_cleaned
                },
                severity="info"
            )

            return True

        except Exception as e:
            print(f"❌ Data cleanup error: {str(e)}")

            # Log cleanup error
            SecurityEvent.log_event(
                "data_cleanup_error",
                f"Data cleanup failed: {str(e)}",
                event_data={
                    'cleanup_type': self.cleanup_type,
                    'error': str(e)
                },
                severity="error"
            )

            return False

    def _cleanup_security_events(self):
        """Clean up old security events"""
        try:
            print("🧹 Cleaning up old security events...")

            # Clean up resolved events older than specified days
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=self.days_to_keep)

            old_events = SecurityEvent.where('resolved', True)\
                                     .where('created_at', '<', cutoff_date)\
                                     .get()

            count = 0
            for event in old_events:
                event.delete()
                count += 1

            print(f"🗑️ Cleaned up {count} old security events")
            return count

        except Exception as e:
            print(f"❌ Security events cleanup error: {str(e)}")
            return 0

    def _cleanup_otps(self):
        """Clean up expired and used OTPs"""
        try:
            print("🧹 Cleaning up expired OTPs...")

            # Clean up expired OTPs
            expired_count = OTP.cleanup_expired()

            # Clean up used OTPs older than 24 hours
            used_count = OTP.cleanup_used(older_than_hours=24)

            total_count = expired_count + used_count
            print(f"🗑️ Cleaned up {total_count} OTPs ({expired_count} expired, {used_count} used)")

            return total_count

        except Exception as e:
            print(f"❌ OTP cleanup error: {str(e)}")
            return 0

    def _cleanup_inactive_users(self):
        """Clean up inactive user data (not deleting users, just cleaning up related data)"""
        try:
            print("🧹 Cleaning up inactive user data...")

            # Find users who haven't logged in for a very long time (1 year)
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=365)

            # Clean up old login attempts for inactive users
            inactive_users = User.where('last_login_at', '<', cutoff_date)\
                                .or_where('last_login_at', None)\
                                .get()

            count = 0
            for user in inactive_users:
                # Reset login attempts for very old inactive accounts
                if hasattr(user, 'login_attempts') and user.login_attempts > 0:
                    user.login_attempts = 0
                    user.locked_until = None
                    user.save()
                    count += 1

            print(f"🗑️ Reset login data for {count} inactive users")
            return count

        except Exception as e:
            print(f"❌ Inactive users cleanup error: {str(e)}")
            return 0

    def _cleanup_expired_tokens(self):
        """Clean up expired tokens and verification data"""
        try:
            print("🧹 Cleaning up expired tokens...")

            # Clean up expired email verification tokens
            cutoff_date = datetime.now(timezone.utc)

            users_with_expired_tokens = User.where('email_verification_expires', '<', cutoff_date)\
                                           .where('email_verification_token', '!=', None)\
                                           .get()

            count = 0
            for user in users_with_expired_tokens:
                user.email_verification_token = None
                user.email_verification_expires = None
                user.save()
                count += 1

            # Clean up expired password reset tokens
            users_with_expired_reset = User.where('password_reset_expires', '<', cutoff_date)\
                                          .where('password_reset_token', '!=', None)\
                                          .get()

            for user in users_with_expired_reset:
                user.password_reset_token = None
                user.password_reset_expires = None
                user.save()
                count += 1

            print(f"🗑️ Cleaned up {count} expired tokens")
            return count

        except Exception as e:
            print(f"❌ Token cleanup error: {str(e)}")
            return 0

    def _cleanup_old_sessions(self):
        """Clean up old session data (if using database sessions)"""
        try:
            print("🧹 Cleaning up old sessions...")

            # This would clean up database sessions if implemented
            # For now, just return 0

            print("🗑️ Session cleanup not implemented (using default session driver)")
            return 0

        except Exception as e:
            print(f"❌ Session cleanup error: {str(e)}")
            return 0

    def _cleanup_logs(self):
        """Clean up old log files"""
        try:
            print("🧹 Cleaning up old logs...")

            # This would clean up log files if needed
            # For now, just return 0

            print("🗑️ Log cleanup not implemented")
            return 0

        except Exception as e:
            print(f"❌ Log cleanup error: {str(e)}")
            return 0

    def get_cleanup_stats(self):
        """Get statistics about what would be cleaned up"""
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=self.days_to_keep)

            stats = {
                'security_events': SecurityEvent.where('resolved', True)\
                                                .where('created_at', '<', cutoff_date)\
                                                .count(),
                'expired_otps': OTP.where('expires_at', '<', datetime.now(timezone.utc)).count(),
                'used_otps': OTP.where('used', True)\
                               .where('used_at', '<', datetime.now(timezone.utc) - timedelta(hours=24))\
                               .count(),
                'expired_tokens': User.where('email_verification_expires', '<', datetime.now(timezone.utc))\
                                     .where('email_verification_token', '!=', None)\
                                     .count()
            }

            return stats

        except Exception as e:
            print(f"❌ Cleanup stats error: {str(e)}")
            return {}
