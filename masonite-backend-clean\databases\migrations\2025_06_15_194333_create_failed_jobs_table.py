"""CreateFailedJobsTable Migration."""

from masoniteorm.migrations import Migration


class CreateFailedJobsTable(Migration):
    def up(self):
        """
        Run the migrations.
        """
        with self.schema.create("failed_jobs") as table:
            table.increments("id")

            # Failed job information
            table.string("queue").default("default")
            table.text("payload")  # Serialized job data
            table.text("exception")  # Exception details
            table.timestamp("failed_at")

    def down(self):
        """
        Revert the migrations.
        """
        self.schema.drop("failed_jobs")
