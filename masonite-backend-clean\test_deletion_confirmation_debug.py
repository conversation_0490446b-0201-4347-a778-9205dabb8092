#!/usr/bin/env python3
"""
Debug Account Deletion Confirmation Issue
Test the deletion confirmation fix
"""

import requests
import json
import time
import random
import string

# Configuration
BASE_URL = "http://localhost:8001"
API_BASE = f"{BASE_URL}/api"

def generate_test_email():
    """Generate a unique test email"""
    random_suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
    return f"test-confirm-{random_suffix}@example.com"

def test_deletion_confirmation():
    """Test account deletion confirmation"""
    print("🔧 Testing Account Deletion Confirmation...")
    
    email = generate_test_email()
    password = "TestPassword123!"
    
    # 1. Register user
    register_data = {
        "name": "Test User",
        "email": email,
        "password": password,
        "password_confirmation": password,
        "first_name": "Test",
        "last_name": "User"
    }
    
    response = requests.post(f"{API_BASE}/auth/register", json=register_data)
    print(f"📝 Registration: {response.status_code}")
    
    if response.status_code != 201:
        print(f"❌ Registration failed: {response.text}")
        return False
    
    data = response.json()
    token = data.get('token')
    headers = {"Authorization": f"Bearer {token}"}
    
    print(f"✅ User registered successfully")
    
    # 2. Request account deletion
    deletion_request = {
        "preservePaymentData": True,
        "preserveTransactionHistory": False,
        "preserveProfileData": True,
        "preserveSecurityLogs": True,
        "customRetentionPeriod": 60,
        "reason": "Testing deletion confirmation"
    }
    
    response = requests.post(f"{API_BASE}/account/request-deletion", 
                           json=deletion_request, headers=headers)
    print(f"🗑️ Deletion Request: {response.status_code}")
    
    if response.status_code != 200:
        print(f"❌ Deletion request failed: {response.text}")
        return False
    
    deletion_data = response.json()
    confirmation_token = deletion_data.get('confirmationToken')
    print(f"✅ Deletion requested, token: {confirmation_token[:10]}...")
    
    # 3. Test deletion confirmation (this was failing with datetime error)
    print("\n🔍 Testing deletion confirmation...")
    confirm_data = {"token": confirmation_token}
    response = requests.post(f"{API_BASE}/account/confirm-deletion", json=confirm_data)
    print(f"✅ Deletion Confirmation: {response.status_code}")
    print(f"✅ Response: {response.text}")
    
    if response.status_code == 200:
        confirmation_result = response.json()
        print(f"✅ Account deletion confirmed: {confirmation_result.get('message')}")
        return True
    else:
        print(f"❌ Deletion confirmation failed")
        return False

def main():
    """Run the deletion confirmation debug test"""
    print("🐛 Debugging Account Deletion Confirmation Issue")
    print("=" * 50)
    
    # Wait a moment for server to start
    time.sleep(2)
    
    success = test_deletion_confirmation()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ Deletion confirmation issue has been FIXED!")
    else:
        print("❌ Deletion confirmation issue still exists")

if __name__ == "__main__":
    main()
