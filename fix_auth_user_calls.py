#!/usr/bin/env python3
"""
Fix auth.user() calls to request.user() in controllers
"""

import re
import os

def fix_auth_user_calls(file_path):
    """Fix auth.user() calls in a file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Replace auth.user() with request.user()
        updated_content = re.sub(r'auth\.user\(\)', 'request.user()', content)
        
        if content != updated_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(updated_content)
            print(f"✅ Fixed {file_path}")
            return True
        else:
            print(f"ℹ️ No changes needed in {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ Error fixing {file_path}: {str(e)}")
        return False

def main():
    """Fix auth.user() calls in controllers"""
    print("🔧 Fixing auth.user() calls in controllers...")
    
    controllers_to_fix = [
        'app/controllers/NotificationController.py',
        'app/controllers/QueueController.py'
    ]
    
    fixed_count = 0
    for controller in controllers_to_fix:
        if os.path.exists(controller):
            if fix_auth_user_calls(controller):
                fixed_count += 1
        else:
            print(f"❌ File not found: {controller}")
    
    print(f"\n🎯 Fixed {fixed_count} controller files")

if __name__ == "__main__":
    main()
