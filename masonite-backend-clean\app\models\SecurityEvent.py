""" SecurityEvent Model """

from masoniteorm.models import Model
from datetime import datetime, timezone
from enum import Enum
import json


class SecurityEventType(Enum):
    """Security event types for consistent logging"""
    LOGIN_SUCCESS = "login_success"
    LOGIN_FAILED = "login_failed"
    LOGIN_LOCKED = "login_locked"
    LOGOUT = "logout"
    PASSWORD_CHANGED = "password_changed"
    PASSWORD_RESET_REQUESTED = "password_reset_requested"
    PASSWORD_RESET_COMPLETED = "password_reset_completed"
    TWO_FACTOR_ENABLED = "two_factor_enabled"
    TWO_FACTOR_DISABLED = "two_factor_disabled"
    TWO_FACTOR_SUCCESS = "two_factor_success"
    TWO_FACTOR_FAILED = "two_factor_failed"
    OTP_SENT = "otp_sent"
    OTP_VERIFIED = "otp_verified"
    OTP_FAILED = "otp_failed"
    ACCOUNT_CREATED = "account_created"
    ACCOUNT_DELETED = "account_deleted"
    ACCOUNT_LOCKED = "account_locked"
    ACCOUNT_UNLOCKED = "account_unlocked"
    EMAIL_VERIFIED = "email_verified"
    PHONE_VERIFIED = "phone_verified"
    SUSPICIOUS_ACTIVITY = "suspicious_activity"
    RATE_LIMIT_EXCEEDED = "rate_limit_exceeded"
    UNAUTHORIZED_ACCESS = "unauthorized_access"
    DATA_EXPORT_REQUESTED = "data_export_requested"
    GDPR_DELETION_REQUESTED = "gdpr_deletion_requested"


class SecurityEvent(Model):
    """SecurityEvent Model for comprehensive security logging"""

    __fillable__ = [
        'event_type', 'event_category', 'severity', 'message', 'event_data',
        'user_id', 'session_id', 'ip_address', 'user_agent', 'endpoint',
        'method', 'referer', 'risk_level', 'requires_action', 'resolved',
        'resolved_at', 'resolved_by'
    ]

    __casts__ = {
        'requires_action': 'boolean',
        'resolved': 'boolean'
    }

    __dates__ = ['resolved_at', 'created_at', 'updated_at']

    @classmethod
    def log_event(cls, event_type, message, user_id=None, event_data=None,
                  ip_address=None, user_agent=None, endpoint=None, method=None,
                  severity="info", risk_level="low", requires_action=False):
        """Log a security event"""

        # Convert event_data to JSON string if it's a dict
        if event_data and isinstance(event_data, dict):
            event_data = json.dumps(event_data)

        return cls.create({
            'event_type': event_type,
            'event_category': 'security',
            'severity': severity,
            'message': message,
            'event_data': event_data,
            'user_id': str(user_id) if user_id else None,
            'ip_address': ip_address,
            'user_agent': user_agent,
            'endpoint': endpoint,
            'method': method,
            'risk_level': risk_level,
            'requires_action': requires_action,
            'resolved': not requires_action  # Auto-resolve if no action required
        })

    @classmethod
    def log_login_success(cls, user_id, ip_address=None, user_agent=None):
        """Log successful login"""
        return cls.log_event(
            SecurityEventType.LOGIN_SUCCESS.value,
            f"User {user_id} logged in successfully",
            user_id=user_id,
            ip_address=ip_address,
            user_agent=user_agent,
            severity="info"
        )

    @classmethod
    def log_login_failed(cls, identifier, reason, ip_address=None, user_agent=None, user_id=None):
        """Log failed login attempt"""
        return cls.log_event(
            SecurityEventType.LOGIN_FAILED.value,
            f"Failed login attempt for {identifier}: {reason}",
            user_id=user_id,
            event_data={'identifier': identifier, 'reason': reason},
            ip_address=ip_address,
            user_agent=user_agent,
            severity="warning",
            risk_level="medium"
        )

    @classmethod
    def log_account_locked(cls, user_id, reason, ip_address=None, user_agent=None):
        """Log account lockout"""
        return cls.log_event(
            SecurityEventType.ACCOUNT_LOCKED.value,
            f"Account {user_id} locked: {reason}",
            user_id=user_id,
            event_data={'reason': reason},
            ip_address=ip_address,
            user_agent=user_agent,
            severity="error",
            risk_level="high",
            requires_action=True
        )

    @classmethod
    def log_suspicious_activity(cls, description, user_id=None, ip_address=None,
                               user_agent=None, event_data=None):
        """Log suspicious activity"""
        return cls.log_event(
            SecurityEventType.SUSPICIOUS_ACTIVITY.value,
            f"Suspicious activity detected: {description}",
            user_id=user_id,
            event_data=event_data,
            ip_address=ip_address,
            user_agent=user_agent,
            severity="error",
            risk_level="high",
            requires_action=True
        )

    @classmethod
    def log_rate_limit_exceeded(cls, endpoint, ip_address=None, user_agent=None, user_id=None):
        """Log rate limit exceeded"""
        return cls.log_event(
            SecurityEventType.RATE_LIMIT_EXCEEDED.value,
            f"Rate limit exceeded for endpoint {endpoint}",
            user_id=user_id,
            event_data={'endpoint': endpoint},
            ip_address=ip_address,
            user_agent=user_agent,
            endpoint=endpoint,
            severity="warning",
            risk_level="medium"
        )

    @classmethod
    def log_otp_event(cls, event_type, identifier, user_id=None, ip_address=None, user_agent=None):
        """Log OTP-related events"""
        messages = {
            SecurityEventType.OTP_SENT.value: f"OTP sent to {identifier}",
            SecurityEventType.OTP_VERIFIED.value: f"OTP verified for {identifier}",
            SecurityEventType.OTP_FAILED.value: f"OTP verification failed for {identifier}"
        }

        severity = "warning" if event_type == SecurityEventType.OTP_FAILED.value else "info"

        return cls.log_event(
            event_type,
            messages.get(event_type, f"OTP event: {event_type}"),
            user_id=user_id,
            event_data={'identifier': identifier},
            ip_address=ip_address,
            user_agent=user_agent,
            severity=severity
        )

    def get_event_data_dict(self):
        """Get event data as dictionary"""
        if self.event_data:
            try:
                return json.loads(self.event_data)
            except json.JSONDecodeError:
                return {}
        return {}

    def mark_resolved(self, resolved_by=None):
        """Mark security event as resolved"""
        self.resolved = True
        self.resolved_at = datetime.now(timezone.utc)
        self.resolved_by = resolved_by
        self.save()

    @classmethod
    def get_unresolved_events(cls):
        """Get all unresolved security events"""
        return cls.where('resolved', False).where('requires_action', True).get()

    @classmethod
    def get_events_by_user(cls, user_id, limit=50):
        """Get security events for a specific user"""
        return cls.where('user_id', str(user_id))\
                  .order_by('created_at', 'desc')\
                  .limit(limit)\
                  .get()

    @classmethod
    def get_events_by_ip(cls, ip_address, limit=50):
        """Get security events for a specific IP address"""
        return cls.where('ip_address', ip_address)\
                  .order_by('created_at', 'desc')\
                  .limit(limit)\
                  .get()

    @classmethod
    def get_high_risk_events(cls, limit=100):
        """Get high-risk security events"""
        return cls.where('risk_level', 'high')\
                  .or_where('risk_level', 'critical')\
                  .order_by('created_at', 'desc')\
                  .limit(limit)\
                  .get()

    @classmethod
    def cleanup_old_events(cls, days_to_keep=90):
        """Clean up old security events"""
        cutoff_date = datetime.now(timezone.utc) - timezone.timedelta(days=days_to_keep)
        old_events = cls.where('created_at', '<', cutoff_date)\
                        .where('resolved', True)\
                        .get()

        count = len(old_events)
        for event in old_events:
            event.delete()

        return count

    def __str__(self):
        return f"SecurityEvent({self.event_type}, {self.severity}, {self.created_at})"
