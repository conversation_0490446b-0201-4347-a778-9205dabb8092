"""
Masonite 4 API Authentication Controller
Maintains exact LoopBack API compatibility for existing frontend
"""

from masonite.controllers import Controller
from masonite.request import Request
from masonite.response import Response
from masonite.authentication import Auth
from masonite.validation import Validator
from masonite.mail import Mail
from app.models.User import User
from app.mailables.EmailVerification import EmailVerification
from app.mailables.PasswordReset import Password<PERSON>eset
from datetime import datetime, timedelta


class AuthController(Controller):
    """
    API Authentication Controller 
    Maintains exact compatibility with LoopBack endpoints
    """
    
    def login(self, request: Request, response: Response, auth: Auth, validate: Validator):
        """
        POST /api/auth/login
        User login with email/username and password
        Returns JWT token compatible with LoopBack format
        """
        # Validate request using built-in Masonite validator
        errors = request.validate(
            validate.required(['email', 'password']),
            validate.email('email'),
            validate.string('password')        )
        
        if errors:
            return response.json({
                'error': {
                    'statusCode': 422,
                    'name': 'ValidationError',
                    'message': 'The request body is invalid',
                    'details': errors.all()
                }
            }, 422)
        
        email = request.input('email')
        password = request.input('password')
        
        # Use built-in Masonite authentication
        user = auth.attempt(email, password)
        
        if not user:
            return response.json({
                'error': {
                    'statusCode': 401,
                    'name': 'UnauthorizedError',
                    'message': 'Invalid email or password'
                }
            }, 401)
          # Generate JWT token using Masonite's built-in token generation
        token = user.generate_api_token()
        
        # Return LoopBack-compatible response
        return response.json({
            'token': token,
            'user': {
                'id': user.id,
                'name': user.name,
                'email': user.email,
                'emailVerified': user.is_email_verified(),                'twoFactorEnabled': user.two_factor_enabled or False
            }
        })
    
    def register(self, request: Request, response: Response, auth: Auth, validate: Validator):
        """
        POST /api/auth/register
        User registration compatible with LoopBack format
        """        # Validate registration data with strong password requirements
        errors = request.validate(
            validate.required(['name', 'email', 'password', 'password_confirmation']),
            validate.email('email'),
            validate.string('name'),
            validate.strong('password', length=8, special=1, uppercase=1),  # Built-in strong password validation
            validate.confirmed('password')  # Built-in password confirmation validation
        )
        
        if errors:
            return response.json({
                'error': {
                    'statusCode': 422,
                    'name': 'ValidationError',
                    'message': 'The request body is invalid',
                    'details': errors.all()
                }
            }, 422)
        
        # Use built-in Masonite auth.register
        user = auth.register({
            'name': request.input('name'),            'email': request.input('email'),
            'password': request.input('password')
        })
        
        if not user:
            return response.json({
                'error': {
                    'statusCode': 422,
                    'name': 'ValidationError',
                    'message': 'User registration failed'
                }            }, 422)
          # Generate email verification token and send verification email
        verification_token = user.generate_email_verification_token()
        
        try:
            # Send email verification email using correct Masonite syntax
            mailable = EmailVerification(user, verification_token).to(user.email)
            Mail.mailable(mailable).send()
        except Exception as e:
            # Log the error but don't fail registration
            print(f"Failed to send verification email: {e}")
        
        # Generate JWT token
        token = user.generate_api_token()
        
        # Return LoopBack-compatible response
        return response.json({
            'token': token,
            'user': {
                'id': user.id,                'name': user.name,
                'email': user.email,
                'emailVerified': False,
                'twoFactorEnabled': False
            }
        }, 201)
    
    def logout(self, request: Request, response: Response):
        """
        POST /api/auth/logout
        User logout - invalidate token
        """
        # Get the current user from request (set by middleware)
        user = request.user()
        if user:
            # Invalidate the JWT token by clearing api_token
            user.api_token = None
            user.save()
        
        return response.json({
            'message': 'Logged out successfully'
        })
    
    def refresh(self, request: Request, response: Response):
        """
        POST /api/auth/refresh
        Refresh JWT token
        """
        user = request.user()
        if not user:
            return response.json({
                'error': {
                    'statusCode': 401,
                    'name': 'UnauthorizedError',
                    'message': 'No valid token provided'
                }
            }, 401)
        
        # Generate new JWT token
        token = user.generate_api_token()
        
        return response.json({
            'token': token,
            'user': {                'id': user.id,
                'name': user.name,
                'email': user.email,
                'emailVerified': user.is_email_verified(),
                'twoFactorEnabled': user.two_factor_enabled or False
            }
        })
    
    def verify_email(self, request: Request, response: Response, validate: Validator):
        """
        POST /api/auth/verify-email
        Email verification endpoint - compatible with LoopBack
        """
        # Validate request using built-in Masonite validator
        errors = request.validate(
            validate.required(['token']),
            validate.string('token')
        )
        
        if errors:
            return response.json({
                'error': {
                    'statusCode': 422,
                    'name': 'ValidationError',
                    'message': 'The request body is invalid',
                    'details': errors.all()
                }
            }, 422)
        
        token = request.input('token')
        
        # Find user with valid verification token
        user = User.where('email_verification_token', token)\
                   .where('email_verification_expires', '>', datetime.now())\
                   .first()
        
        if not user:
            return response.json({
                'error': {
                    'statusCode': 400,
                    'name': 'BadRequestError',
                    'message': 'Invalid or expired verification token'
                }
            }, 400)
        
        # Mark email as verified
        user.mark_email_as_verified()
        
        # Return LoopBack-compatible response
        return response.json({
            'message': 'Email verified successfully',
            'user': {
                'id': user.id,                'email': user.email,
                'firstName': user.first_name or user.name,
                'emailVerified': True
            }
        })
    
    def forgot_password(self, request: Request, response: Response, validate: Validator):
        """
        POST /api/auth/forgot-password
        Password reset request - compatible with LoopBack
        """
        # Validate request using built-in Masonite validator
        errors = request.validate(
            validate.required(['email']),
            validate.email('email')
        )
        
        if errors:
            return response.json({
                'error': {
                    'statusCode': 422,
                    'name': 'ValidationError',
                    'message': 'The request body is invalid',
                    'details': errors.all()
                }
            }, 422)        
        email = request.input('email')
        
        # Find user by email
        user = User.where('email', email).first()
        
        if not user:
            # Don't reveal if email exists or not (security best practice)
            return response.json({
                'message': 'If the email exists, a password reset link has been sent'
            })
        
        # Generate password reset token
        reset_token = user.generate_password_reset_token()        
        try:
            # Send password reset email using built-in Mail system
            mailable = PasswordReset(user, reset_token).to(user.email)
            Mail.mailable(mailable).send()
        except Exception as e:
            # Log the error but don't reveal it to prevent information disclosure
            print(f"Failed to send password reset email: {e}")
        
        return response.json({
            'message': 'If the email exists, a password reset link has been sent'
        })
    
    def reset_password(self, request: Request, response: Response, validate: Validator):
        """
        POST /api/auth/reset-password
        Password reset execution - compatible with LoopBack
        """        # Validate request with strong password requirements
        errors = request.validate(
            validate.required(['token', 'password']),
            validate.string('token'),
            validate.strong('password', length=8, special=1, uppercase=1)  # Built-in strong password validation
        )
        
        if errors:
            return response.json({
                'error': {
                    'statusCode': 422,
                    'name': 'ValidationError',
                    'message': 'The request body is invalid',
                    'details': errors.all()
                }
            }, 422)
        
        token = request.input('token')
        new_password = request.input('password')
        
        # Find user with valid reset token
        user = User.where('password_reset_token', token)\
                   .where('password_reset_expires', '>', datetime.now())\
                   .first()
        
        if not user:
            return response.json({
                'error': {
                    'statusCode': 400,
                    'name': 'BadRequestError',
                    'message': 'Invalid or expired reset token'
                }
            }, 400)
        
        # Update password and clear reset token
        user.set_password(new_password)
        user.clear_password_reset_token()
        
        return response.json({
            'message': 'Password reset successfully'
        })
    
    def profile(self, request: Request, response: Response):
        """
        GET /api/auth/profile
        Get current authenticated user profile
        Protected endpoint that requires valid JWT token
        """
        # The user should be set by the JWT middleware
        user = request.user()
        
        if not user:
            return response.json({
                'error': {
                    'statusCode': 401,
                    'name': 'UnauthorizedError',
                    'message': 'Authentication required'
                }
            }, 401)
        
        # Return user profile in LoopBack-compatible format
        return response.json({
            'user': {
                'id': user.id,
                'name': user.name,
                'email': user.email,
                'emailVerified': user.is_email_verified(),
                'twoFactorEnabled': user.two_factor_enabled or False,
                'createdAt': user.created_at.isoformat() if user.created_at else None,
                'updatedAt': user.updated_at.isoformat() if user.updated_at else None
            }
        })
