#!/usr/bin/env python3
"""
Test Account Deletion and OTP System Fixes
Tests the fixes for datetime serialization and OTP verification errors
"""

import requests
import json
import time
import random
import string

# Configuration
BASE_URL = "http://localhost:8001"
API_BASE = f"{BASE_URL}/api"

def generate_test_email():
    """Generate a unique test email"""
    random_suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
    return f"test-fixes-{random_suffix}@example.com"

def test_user_registration_and_auth():
    """Test user registration and authentication"""
    print("🔧 Testing User Registration and Authentication...")
    
    email = generate_test_email()
    password = "TestPassword123!"
    
    # Register user
    register_data = {
        "name": "Test User",
        "email": email,
        "password": password,
        "password_confirmation": password,
        "first_name": "Test",
        "last_name": "User"
    }
    
    response = requests.post(f"{API_BASE}/auth/register", json=register_data)
    print(f"📝 Registration: {response.status_code}")
    
    if response.status_code != 201:
        print(f"❌ Registration failed: {response.text}")
        return None, None
    
    data = response.json()
    token = data.get('token')
    user_id = data.get('user', {}).get('id')
    
    print(f"✅ User registered: {user_id}")
    return token, email

def test_account_deletion_flow(token, email):
    """Test complete account deletion flow with datetime fixes"""
    print("\n🗑️ Testing Account Deletion Flow...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 1. Request account deletion
    deletion_request = {
        "preservePaymentData": True,
        "preserveTransactionHistory": False,
        "preserveProfileData": True,
        "preserveSecurityLogs": True,
        "customRetentionPeriod": 60,
        "reason": "Testing account deletion system with fixes"
    }
    
    response = requests.post(f"{API_BASE}/account/request-deletion", 
                           json=deletion_request, headers=headers)
    print(f"🗑️ Deletion Request: {response.status_code}")
    
    if response.status_code != 200:
        print(f"❌ Deletion request failed: {response.text}")
        return False
    
    deletion_data = response.json()
    confirmation_token = deletion_data.get('confirmationToken')
    print(f"✅ Deletion requested, token: {confirmation_token[:10]}...")
    
    # 2. Check deletion status (this was failing with datetime error)
    response = requests.get(f"{API_BASE}/account/deletion-status", headers=headers)
    print(f"📊 Deletion Status: {response.status_code}")
    
    if response.status_code != 200:
        print(f"❌ Deletion status check failed: {response.text}")
        return False
    
    status_data = response.json()
    print(f"✅ Deletion status retrieved: {status_data.get('hasPendingDeletion')}")
    
    # 3. Export data before deletion
    response = requests.get(f"{API_BASE}/account/export-data", headers=headers)
    print(f"📤 Data Export: {response.status_code}")
    
    if response.status_code != 200:
        print(f"❌ Data export failed: {response.text}")
        return False
    
    print("✅ Data export successful")
    
    # 4. Confirm deletion (this was failing with datetime error)
    confirm_data = {"token": confirmation_token}
    response = requests.post(f"{API_BASE}/account/confirm-deletion", json=confirm_data)
    print(f"✅ Deletion Confirmation: {response.status_code}")
    
    if response.status_code != 200:
        print(f"❌ Deletion confirmation failed: {response.text}")
        return False
    
    confirmation_result = response.json()
    print(f"✅ Account deletion confirmed: {confirmation_result.get('message')}")
    
    # 5. Check preserved data
    preserved_check = {"email": email}
    response = requests.post(f"{API_BASE}/account/check-preserved-data", json=preserved_check)
    print(f"🔍 Preserved Data Check: {response.status_code}")
    
    if response.status_code == 200:
        print("✅ Preserved data check successful")
    
    return True

def test_otp_system_fixes():
    """Test OTP system with verification fixes"""
    print("\n📱 Testing OTP System Fixes...")
    
    email = generate_test_email()
    phone = "+**********"
    
    # 1. Send email OTP
    otp_request = {
        "email": email,
        "type": "verification"
    }
    
    response = requests.post(f"{API_BASE}/otp/send-email", json=otp_request)
    print(f"📧 Email OTP Send: {response.status_code}")
    
    if response.status_code != 200:
        print(f"❌ Email OTP send failed: {response.text}")
        return False
    
    print("✅ Email OTP sent successfully")
    
    # 2. Send SMS OTP
    sms_request = {
        "phone": phone,
        "type": "verification"
    }
    
    response = requests.post(f"{API_BASE}/otp/send-sms", json=sms_request)
    print(f"📱 SMS OTP Send: {response.status_code}")
    
    if response.status_code != 200:
        print(f"❌ SMS OTP send failed: {response.text}")
        return False
    
    print("✅ SMS OTP sent successfully")
    
    # 3. Check OTP status
    status_request = {
        "identifier": email,
        "type": "verification"
    }
    
    response = requests.get(f"{API_BASE}/otp/status", params=status_request)
    print(f"📊 OTP Status: {response.status_code}")
    
    if response.status_code != 200:
        print(f"❌ OTP status check failed: {response.text}")
        return False
    
    print("✅ OTP status check successful")
    
    # 4. Test OTP verification (this was failing with 'str' object has no attribute 'where')
    verify_request = {
        "identifier": email,
        "code": "123456",  # Invalid code to test error handling
        "type": "verification"
    }
    
    response = requests.post(f"{API_BASE}/otp/verify", json=verify_request)
    print(f"🔍 OTP Verification: {response.status_code}")
    
    # Should return 200 with valid: false, not a 500 error
    if response.status_code == 200:
        verify_result = response.json()
        print(f"✅ OTP verification handled correctly: {verify_result.get('valid', False)}")
        return True
    else:
        print(f"❌ OTP verification failed with error: {response.text}")
        return False

def test_email_verification_during_registration():
    """Test email verification during user registration"""
    print("\n📧 Testing Email Verification During Registration...")
    
    email = generate_test_email()
    password = "TestPassword123!"
    
    # Register user (should send verification email)
    register_data = {
        "name": "Test EmailVerify",
        "email": email,
        "password": password,
        "password_confirmation": password,
        "first_name": "Test",
        "last_name": "EmailVerify"
    }
    
    response = requests.post(f"{API_BASE}/auth/register", json=register_data)
    print(f"📝 Registration with Email Verification: {response.status_code}")
    
    if response.status_code == 201:
        print("✅ Registration successful - email verification should be sent")
        return True
    else:
        print(f"❌ Registration failed: {response.text}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Account Deletion and OTP System Fixes")
    print("=" * 60)
    
    # Test 1: User Registration and Authentication
    token, email = test_user_registration_and_auth()
    if not token:
        print("❌ Cannot proceed without authentication")
        return
    
    # Test 2: Account Deletion Flow (datetime fixes)
    deletion_success = test_account_deletion_flow(token, email)
    
    # Test 3: OTP System (verification fixes)
    otp_success = test_otp_system_fixes()
    
    # Test 4: Email Verification During Registration
    email_verification_success = test_email_verification_during_registration()
    
    # Summary
    print("\n" + "=" * 60)
    print("🏁 TEST SUMMARY")
    print("=" * 60)
    print(f"✅ Account Deletion Flow: {'PASSED' if deletion_success else 'FAILED'}")
    print(f"✅ OTP System Fixes: {'PASSED' if otp_success else 'FAILED'}")
    print(f"✅ Email Verification: {'PASSED' if email_verification_success else 'FAILED'}")
    
    if deletion_success and otp_success and email_verification_success:
        print("\n🎉 ALL TESTS PASSED - Fixes are working correctly!")
    else:
        print("\n❌ Some tests failed - please check the errors above")

if __name__ == "__main__":
    main()
