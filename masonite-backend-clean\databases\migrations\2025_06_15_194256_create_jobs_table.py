"""CreateJobsTable Migration."""

from masoniteorm.migrations import Migration


class CreateJobsTable(Migration):
    def up(self):
        """
        Run the migrations.
        """
        with self.schema.create("jobs") as table:
            table.increments("id")

            # Queue information
            table.string("queue").default("default").index()
            table.text("payload")  # Serialized job data
            table.integer("attempts").default(0)
            table.integer("reserved_at").nullable()
            table.integer("available_at").index()
            table.timestamp("created_at")

    def down(self):
        """
        Revert the migrations.
        """
        self.schema.drop("jobs")
