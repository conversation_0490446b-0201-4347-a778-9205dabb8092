#!/usr/bin/env python3
"""
Simple notification test to isolate the issue
"""

import requests
import json
import time
import random
import string

BASE_URL = "http://localhost:8001"
API_BASE = f"{BASE_URL}/api"

def generate_test_email():
    """Generate unique test email"""
    timestamp = int(time.time())
    random_str = ''.join(random.choices(string.ascii_lowercase, k=6))
    return f"test-{timestamp}-{random_str}@example.com"

def get_auth_token():
    """Get authentication token"""
    try:
        email = generate_test_email()
        register_data = {
            "email": email,
            "password": "TestPassword123!",
            "password_confirmation": "TestPassword123!",
            "name": "Test User"
        }
        
        response = requests.post(f"{API_BASE}/auth/register", json=register_data, timeout=10)
        if response.status_code == 201:
            data = response.json()
            return data.get('token'), data.get('user', {}).get('id')
        else:
            print(f"Registration failed: {response.status_code} - {response.text}")
            return None, None
    except Exception as e:
        print(f"Registration error: {str(e)}")
        return None, None

def test_simple_notification(token):
    """Test the simplest possible notification"""
    headers = {'Authorization': f'Bearer {token}'}
    
    # Test with minimal valid data
    data = {
        'type': 'security_alert',
        'message': 'Simple test'
    }
    
    print(f"🧪 Testing simple notification...")
    print(f"   Request: POST {API_BASE}/notifications/test")
    print(f"   Headers: {headers}")
    print(f"   Data: {json.dumps(data, indent=2)}")
    
    try:
        response = requests.post(f"{API_BASE}/notifications/test", json=data, headers=headers, timeout=10)
        print(f"   Response Status: {response.status_code}")
        print(f"   Response Headers: {dict(response.headers)}")
        
        try:
            response_data = response.json()
            print(f"   Response Body: {json.dumps(response_data, indent=2)}")
        except:
            print(f"   Response Text: {response.text}")
            
        return response.status_code == 200
        
    except Exception as e:
        print(f"   ❌ Request Error: {str(e)}")
        return False

def test_notification_endpoint_exists(token):
    """Test if the notification endpoint exists"""
    headers = {'Authorization': f'Bearer {token}'}
    
    print(f"\n🔍 Testing if notification endpoint exists...")
    
    # Test with GET request (should return 405 Method Not Allowed)
    try:
        response = requests.get(f"{API_BASE}/notifications/test", headers=headers, timeout=10)
        print(f"   GET /notifications/test: {response.status_code}")
        
        if response.status_code == 405:
            print("   ✅ Endpoint exists (Method Not Allowed is expected)")
        elif response.status_code == 404:
            print("   ❌ Endpoint does not exist")
        else:
            print(f"   ⚠️ Unexpected status: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Request Error: {str(e)}")

def main():
    """Simple notification test"""
    print("🔍 Simple Notification Test")
    
    # Get auth token
    token, user_id = get_auth_token()
    if not token:
        print("❌ Cannot get auth token")
        return
    
    print(f"✅ Got auth token for user {user_id}: {token[:20]}...")
    
    # Test if endpoint exists
    test_notification_endpoint_exists(token)
    
    # Test simple notification
    success = test_simple_notification(token)
    
    if success:
        print("\n✅ Notification test passed!")
    else:
        print("\n❌ Notification test failed!")

if __name__ == "__main__":
    main()
