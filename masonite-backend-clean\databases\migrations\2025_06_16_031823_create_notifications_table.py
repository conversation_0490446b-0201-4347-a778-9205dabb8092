"""CreateNotificationsTable Migration."""

from masoniteorm.migrations import Migration


class CreateNotificationsTable(Migration):
    def up(self):
        """
        Run the migrations.
        """
        with self.schema.create("notifications") as table:
            table.increments("id")
            table.integer("user_id").unsigned()
            table.string("type")
            table.string("title")
            table.text("message")
            table.json("data").nullable()
            table.boolean("read").default(False)
            table.timestamp("read_at").nullable()
            table.timestamps()

            # Foreign key constraint
            table.foreign("user_id").references("id").on("users").on_delete("cascade")

    def down(self):
        """
        Revert the migrations.
        """
        self.schema.drop("notifications")
