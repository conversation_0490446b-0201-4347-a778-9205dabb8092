#!/usr/bin/env python3
"""
Debug OTP System Issue
Test the OTP verification fix
"""

import requests
import json
import time
import random
import string

# Configuration
BASE_URL = "http://localhost:8001"
API_BASE = f"{BASE_URL}/api"

def generate_test_email():
    """Generate a unique test email"""
    random_suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
    return f"test-otp-{random_suffix}@example.com"

def test_otp_system():
    """Test OTP system with verification fixes"""
    print("📱 Testing OTP System...")
    
    email = generate_test_email()
    phone = "+1234567890"
    
    # 1. Send email OTP
    print("\n📧 Testing Email OTP...")
    otp_request = {
        "email": email,
        "type": "verification"
    }
    
    response = requests.post(f"{API_BASE}/otp/send-email", json=otp_request)
    print(f"📧 Email OTP Send: {response.status_code}")
    print(f"📧 Response: {response.text}")
    
    if response.status_code != 200:
        print(f"❌ Email OTP send failed")
        return False
    
    print("✅ Email OTP sent successfully")
    
    # 2. Send SMS OTP
    print("\n📱 Testing SMS OTP...")
    sms_request = {
        "phone": phone,
        "type": "verification"
    }
    
    response = requests.post(f"{API_BASE}/otp/send-sms", json=sms_request)
    print(f"📱 SMS OTP Send: {response.status_code}")
    print(f"📱 Response: {response.text}")
    
    if response.status_code != 200:
        print(f"❌ SMS OTP send failed")
        return False
    
    print("✅ SMS OTP sent successfully")
    
    # 3. Check OTP status
    print("\n📊 Testing OTP Status...")
    status_request = {
        "identifier": email,
        "type": "verification"
    }
    
    response = requests.get(f"{API_BASE}/otp/status", params=status_request)
    print(f"📊 OTP Status: {response.status_code}")
    print(f"📊 Response: {response.text}")
    
    if response.status_code != 200:
        print(f"❌ OTP status check failed")
        return False
    
    print("✅ OTP status check successful")
    
    # 4. Test OTP verification (this was failing with 'str' object has no attribute 'where')
    print("\n🔍 Testing OTP Verification...")
    verify_request = {
        "identifier": email,
        "code": "123456",  # Invalid code to test error handling
        "type": "verification"
    }
    
    response = requests.post(f"{API_BASE}/otp/verify", json=verify_request)
    print(f"🔍 OTP Verification: {response.status_code}")
    print(f"🔍 Response: {response.text}")
    
    # Should return 200 with valid: false, not a 500 error
    if response.status_code == 200:
        verify_result = response.json()
        print(f"✅ OTP verification handled correctly: {verify_result.get('valid', False)}")
        return True
    else:
        print(f"❌ OTP verification failed with error")
        return False

def main():
    """Run the OTP debug test"""
    print("🐛 Debugging OTP System Issue")
    print("=" * 40)
    
    # Wait a moment for server to start
    time.sleep(2)
    
    success = test_otp_system()
    
    print("\n" + "=" * 40)
    if success:
        print("✅ OTP system issue has been FIXED!")
    else:
        print("❌ OTP system issue still exists")

if __name__ == "__main__":
    main()
