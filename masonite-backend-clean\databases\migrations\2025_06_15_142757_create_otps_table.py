"""CreateOtpsTable Migration."""

from masoniteorm.migrations import Migration


class CreateOtpsTable(Migration):
    def up(self):
        """
        Run the migrations.
        """
        with self.schema.create("otps") as table:
            table.increments("id")

            # OTP identification
            table.string("identifier").index()  # email or phone
            table.string("code_hash")  # hashed OTP code
            table.string("otp_type").default("login")  # login, verification, 2fa
            table.string("delivery_method").default("email")  # email, sms, call

            # Expiration and usage tracking
            table.timestamp("expires_at").index()
            table.boolean("used").default(False)
            table.timestamp("used_at").nullable()

            # Security and rate limiting
            table.integer("attempts").default(0)
            table.integer("max_attempts").default(3)
            table.string("user_id").nullable().index()
            table.text("metadata").nullable()
            table.string("ip_address").nullable()
            table.text("user_agent").nullable()

            table.timestamps()

    def down(self):
        """
        Revert the migrations.
        """
        self.schema.drop("otps")
