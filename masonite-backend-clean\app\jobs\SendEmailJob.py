from masonite.queues import Queueable
from masonite.mail import Mail
from masonite.notification import Notification
from app.models.User import User
from app.models.SecurityEvent import SecurityEvent, SecurityEventType
from datetime import datetime, timezone
import json


class SendEmailJob(Queueable):
    """Background job for sending emails asynchronously"""

    def __init__(self, email_type, recipient_id, data=None, template=None):
        """
        Initialize email job

        Args:
            email_type: Type of email (otp, security_alert, account_change, etc.)
            recipient_id: User ID of the recipient
            data: Email data (subject, content, etc.)
            template: Email template to use
        """
        self.email_type = email_type
        self.recipient_id = recipient_id
        self.data = data or {}
        self.template = template

    def handle(self, mail: Mail):
        """Process the email sending job"""
        try:
            print(f"📧 Processing email job: {self.email_type} for user {self.recipient_id}")

            # Get recipient user
            user = User.find(self.recipient_id)
            if not user:
                print(f"❌ User {self.recipient_id} not found for email job")
                return False

            # Send email based on type
            success = False
            if self.email_type == "otp":
                success = self._send_otp_email(user, mail)
            elif self.email_type == "security_alert":
                success = self._send_security_alert_email(user, mail)
            elif self.email_type == "account_change":
                success = self._send_account_change_email(user, mail)
            elif self.email_type == "welcome":
                success = self._send_welcome_email(user, mail)
            elif self.email_type == "password_reset":
                success = self._send_password_reset_email(user, mail)
            else:
                success = self._send_generic_email(user, mail)

            if success:
                print(f"✅ Email sent successfully: {self.email_type} to {user.email}")

                # Log successful email sending
                SecurityEvent.log_event(
                    "email_sent",
                    f"Email sent successfully: {self.email_type}",
                    user_id=user.id,
                    event_data={
                        'email_type': self.email_type,
                        'recipient': user.email,
                        'template': self.template
                    },
                    severity="info"
                )
            else:
                print(f"❌ Failed to send email: {self.email_type} to {user.email}")

                # Log failed email sending
                SecurityEvent.log_event(
                    "email_failed",
                    f"Failed to send email: {self.email_type}",
                    user_id=user.id,
                    event_data={
                        'email_type': self.email_type,
                        'recipient': user.email,
                        'error': 'Email sending failed'
                    },
                    severity="warning"
                )

            return success

        except Exception as e:
            print(f"❌ Email job error: {str(e)}")

            # Log email job error
            SecurityEvent.log_event(
                "email_job_error",
                f"Email job failed with error: {str(e)}",
                user_id=self.recipient_id,
                event_data={
                    'email_type': self.email_type,
                    'error': str(e)
                },
                severity="error"
            )

            return False

    def _send_otp_email(self, user, mail):
        """Send OTP email"""
        try:
            otp_code = self.data.get('otp_code', '000000')
            otp_type = self.data.get('otp_type', 'login')
            expires_in = self.data.get('expires_in_minutes', 10)

            subject = f"Your verification code: {otp_code}"
            content = f"""
Dear {user.name},

Your verification code for {otp_type} is: {otp_code}

This code will expire in {expires_in} minutes.

For your security, do not share this code with anyone.

Best regards,
SecureApp Team
"""

            mail.to(user.email).subject(subject).text(content).send()
            return True

        except Exception as e:
            print(f"❌ OTP email error: {str(e)}")
            return False

    def _send_security_alert_email(self, user, mail):
        """Send security alert email"""
        try:
            alert_type = self.data.get('alert_type', 'Security Alert')
            message = self.data.get('message', 'A security event occurred')
            severity = self.data.get('severity', 'medium')

            subject = f"🔒 Security Alert: {alert_type}"
            if severity == "critical":
                subject = f"🚨 CRITICAL Security Alert: {alert_type}"

            content = f"""
Dear {user.name},

We detected a security event that requires your attention:

Alert: {alert_type}
Severity: {severity.upper()}
Message: {message}

Please review your account activity and contact support if needed.

Best regards,
SecureApp Security Team
"""

            mail.to(user.email).subject(subject).text(content).send()
            return True

        except Exception as e:
            print(f"❌ Security alert email error: {str(e)}")
            return False

    def _send_account_change_email(self, user, mail):
        """Send account change email"""
        try:
            change_type = self.data.get('change_type', 'Account Update')
            description = self.data.get('description', 'Your account has been updated')

            subject = f"Account Update: {change_type}"
            content = f"""
Dear {user.name},

Your account has been updated:

Change: {change_type}
Description: {description}

If you did not make this change, please contact support immediately.

Best regards,
SecureApp Team
"""

            mail.to(user.email).subject(subject).text(content).send()
            return True

        except Exception as e:
            print(f"❌ Account change email error: {str(e)}")
            return False

    def _send_welcome_email(self, user, mail):
        """Send welcome email"""
        try:
            subject = "Welcome to SecureApp!"
            content = f"""
Dear {user.name},

Welcome to SecureApp! Your account has been successfully created.

We're excited to have you on board. Here are some next steps:

1. Verify your email address
2. Set up two-factor authentication
3. Complete your profile

If you have any questions, feel free to contact our support team.

Best regards,
SecureApp Team
"""

            mail.to(user.email).subject(subject).text(content).send()
            return True

        except Exception as e:
            print(f"❌ Welcome email error: {str(e)}")
            return False

    def _send_password_reset_email(self, user, mail):
        """Send password reset email"""
        try:
            reset_token = self.data.get('reset_token', '')
            reset_url = f"https://secureapp.com/reset-password?token={reset_token}"

            subject = "Password Reset Request"
            content = f"""
Dear {user.name},

You requested a password reset for your SecureApp account.

Click the link below to reset your password:
{reset_url}

This link will expire in 1 hour.

If you did not request this reset, please ignore this email.

Best regards,
SecureApp Team
"""

            mail.to(user.email).subject(subject).text(content).send()
            return True

        except Exception as e:
            print(f"❌ Password reset email error: {str(e)}")
            return False

    def _send_generic_email(self, user, mail):
        """Send generic email"""
        try:
            subject = self.data.get('subject', 'Notification from SecureApp')
            content = self.data.get('content', 'You have a new notification.')

            mail.to(user.email).subject(subject).text(content).send()
            return True

        except Exception as e:
            print(f"❌ Generic email error: {str(e)}")
            return False
