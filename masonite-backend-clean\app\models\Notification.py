"""Notification Model for storing user notifications"""

from masoniteorm.models import Model
from masoniteorm.relationships import belongs_to
from datetime import datetime, timezone


class Notification(Model):
    """Model for user notifications"""
    
    __table__ = 'notifications'
    __fillable__ = [
        'user_id',
        'type',
        'title',
        'message',
        'data',
        'read',
        'read_at'
    ]
    
    __casts__ = {
        'data': 'json',
        'read': 'bool',
        'read_at': 'datetime'
    }
    
    @belongs_to('user_id', 'id')
    def user(self):
        """Get the user this notification belongs to"""
        from app.models.User import User
        return User
    
    def mark_as_read(self):
        """Mark notification as read"""
        self.read = True
        self.read_at = datetime.now(timezone.utc)
        self.save()
        return self
    
    def mark_as_unread(self):
        """Mark notification as unread"""
        self.read = False
        self.read_at = None
        self.save()
        return self
    
    @classmethod
    def create_notification(cls, user_id, notification_type, title, message, data=None):
        """Create a new notification"""
        return cls.create({
            'user_id': user_id,
            'type': notification_type,
            'title': title,
            'message': message,
            'data': data or {},
            'read': False
        })
    
    @classmethod
    def get_user_notifications(cls, user_id, limit=50, unread_only=False):
        """Get notifications for a user"""
        query = cls.where('user_id', user_id)
        
        if unread_only:
            query = query.where('read', False)
        
        return query.order_by('created_at', 'desc').limit(limit).get()
    
    @classmethod
    def get_unread_count(cls, user_id):
        """Get count of unread notifications for a user"""
        return cls.where('user_id', user_id).where('read', False).count()
    
    @classmethod
    def mark_all_read(cls, user_id):
        """Mark all notifications as read for a user"""
        return cls.where('user_id', user_id).where('read', False).update({
            'read': True,
            'read_at': datetime.now(timezone.utc)
        })
    
    def to_dict(self):
        """Convert notification to dictionary"""
        return {
            'id': self.id,
            'type': self.type,
            'title': self.title,
            'message': self.message,
            'data': self.data,
            'read': self.read,
            'read_at': self.read_at.isoformat() if self.read_at else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
