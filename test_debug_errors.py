#!/usr/bin/env python3
"""
Debug specific errors in the system
"""

import requests
import json
import time
import random
import string

BASE_URL = "http://localhost:8001"
API_BASE = f"{BASE_URL}/api"

def generate_test_email():
    """Generate unique test email"""
    timestamp = int(time.time())
    random_str = ''.join(random.choices(string.ascii_lowercase, k=6))
    return f"test-{timestamp}-{random_str}@example.com"

def get_auth_token():
    """Get authentication token"""
    try:
        email = generate_test_email()
        register_data = {
            "email": email,
            "password": "TestPassword123!",
            "password_confirmation": "TestPassword123!",
            "name": "Test User"
        }
        
        response = requests.post(f"{API_BASE}/auth/register", json=register_data, timeout=10)
        if response.status_code == 201:
            data = response.json()
            return data.get('token')
        else:
            print(f"Registration failed: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"Registration error: {str(e)}")
        return None

def test_endpoint_with_details(endpoint, method='GET', token=None, data=None):
    """Test endpoint and get detailed error info"""
    try:
        headers = {}
        if token:
            headers['Authorization'] = f'Bearer {token}'
        
        if method == 'GET':
            response = requests.get(f"{API_BASE}{endpoint}", headers=headers, timeout=10)
        else:
            response = requests.post(f"{API_BASE}{endpoint}", json=data or {}, headers=headers, timeout=10)
        
        print(f"\n🔍 Testing {method} {endpoint}")
        print(f"Status: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        
        try:
            response_data = response.json()
            print(f"Response: {json.dumps(response_data, indent=2)}")
        except:
            print(f"Response Text: {response.text}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ Error testing {endpoint}: {str(e)}")
        return False

def main():
    """Debug the system"""
    print("🔍 Debugging System Errors")
    
    # Get auth token
    token = get_auth_token()
    if not token:
        print("❌ Cannot get auth token")
        return
    
    print(f"✅ Got auth token: {token[:20]}...")
    
    # Test each problematic endpoint
    endpoints = [
        '/security/events',
        '/security/account-status',
        '/security/dashboard',
        '/notifications',
        '/queue/status'
    ]
    
    for endpoint in endpoints:
        test_endpoint_with_details(endpoint, 'GET', token)
        time.sleep(1)  # Small delay between requests
    
    # Test POST endpoints
    post_endpoints = [
        ('/notifications/test', {'type': 'security_alert', 'message': 'Test'}),
        ('/queue/test-email', {'email_type': 'welcome', 'message': 'Test'})
    ]
    
    for endpoint, data in post_endpoints:
        test_endpoint_with_details(endpoint, 'POST', token, data)
        time.sleep(1)

if __name__ == "__main__":
    main()
