#!/usr/bin/env python3
"""
Debug authentication flow
"""

import requests
import json
import time
import random
import string

BASE_URL = "http://localhost:8001"
API_BASE = f"{BASE_URL}/api"

def generate_test_email():
    """Generate unique test email"""
    timestamp = int(time.time())
    random_str = ''.join(random.choices(string.ascii_lowercase, k=6))
    return f"test-{timestamp}-{random_str}@example.com"

def test_registration_flow():
    """Test the complete registration and authentication flow"""
    try:
        print("🧪 Testing Registration Flow...")
        
        # Generate unique email
        email = generate_test_email()
        
        # Register user
        register_data = {
            "email": email,
            "password": "TestPassword123!",
            "password_confirmation": "TestPassword123!",
            "name": "Test User"
        }
        
        print(f"📧 Registering user: {email}")
        response = requests.post(f"{API_BASE}/auth/register", json=register_data, timeout=10)
        print(f"Registration Status: {response.status_code}")
        
        if response.status_code == 201:
            data = response.json()
            token = data.get('token')
            user_id = data.get('user', {}).get('id')
            
            print(f"✅ Registration successful!")
            print(f"   User ID: {user_id}")
            print(f"   Token: {token[:20]}..." if token else "   Token: None")
            print(f"   Full Response: {json.dumps(data, indent=2)}")
            
            if token:
                return test_authentication_with_token(token, user_id)
            else:
                print("❌ No token received from registration")
                return False
        else:
            print(f"❌ Registration failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Registration error: {str(e)}")
        return False

def test_authentication_with_token(token, user_id):
    """Test authentication with the received token"""
    try:
        print(f"\n🔐 Testing Authentication with Token...")
        
        headers = {'Authorization': f'Bearer {token}'}
        
        # Test profile endpoint (should work with auth)
        print("Testing /auth/profile endpoint...")
        response = requests.get(f"{API_BASE}/auth/profile", headers=headers, timeout=10)
        print(f"Profile Status: {response.status_code}")
        
        if response.status_code == 200:
            profile_data = response.json()
            print(f"✅ Profile retrieved successfully!")
            print(f"   Profile: {json.dumps(profile_data, indent=2)}")
        else:
            print(f"❌ Profile failed: {response.text}")
            return False
        
        # Test a security endpoint
        print("\nTesting /security/account-status endpoint...")
        response = requests.get(f"{API_BASE}/security/account-status", headers=headers, timeout=10)
        print(f"Security Status: {response.status_code}")
        
        if response.status_code == 200:
            security_data = response.json()
            print(f"✅ Security endpoint working!")
            print(f"   Security Data: {json.dumps(security_data, indent=2)}")
            return True
        else:
            print(f"❌ Security endpoint failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Authentication test error: {str(e)}")
        return False

def test_login_flow():
    """Test login with existing user"""
    try:
        print("\n🔑 Testing Login Flow...")
        
        # Try to login with a test user (this might fail if user doesn't exist)
        login_data = {
            "email": "<EMAIL>",
            "password": "TestPassword123!"
        }
        
        response = requests.post(f"{API_BASE}/auth/login", json=login_data, timeout=10)
        print(f"Login Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            token = data.get('token')
            print(f"✅ Login successful!")
            print(f"   Token: {token[:20]}..." if token else "   Token: None")
            return token
        else:
            print(f"❌ Login failed: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Login error: {str(e)}")
        return None

def main():
    """Run authentication debugging"""
    print("🔍 Authentication Flow Debugging")
    print(f"📍 Testing against: {BASE_URL}")
    
    # Test registration flow
    registration_success = test_registration_flow()
    
    if registration_success:
        print("\n✅ Registration and authentication flow working!")
    else:
        print("\n❌ Issues found in authentication flow")
        
        # Try login flow as backup
        token = test_login_flow()
        if token:
            test_authentication_with_token(token, "unknown")

if __name__ == "__main__":
    main()
