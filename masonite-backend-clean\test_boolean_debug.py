#!/usr/bin/env python3
"""
Debug Boolean Casting Issue
Test each step to isolate the boolean error
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.models.AccountDeletionRecord import AccountDeletionRecord
from app.models.User import User

def test_boolean_access():
    """Test accessing boolean fields on AccountDeletionRecord"""
    print("🔧 Testing Boolean Field Access...")
    
    try:
        # Test 1: Create a deletion record
        print("1. Creating deletion record...")
        test_preferences = {
            'preservePaymentData': True,
            'preserveTransactionHistory': False,
            'preserveProfileData': True,
            'preserveSecurityLogs': True,
            'customRetentionPeriod': 60,
            'reason': 'Testing boolean access'
        }
        
        deletion_record = AccountDeletionRecord.create_deletion_request(
            user_id=999,
            email="<EMAIL>",
            preferences=test_preferences
        )
        print(f"✅ Deletion record created: {deletion_record.id}")
        
        # Test 2: Access boolean fields directly
        print("2. Testing direct boolean field access...")
        try:
            preserve_payment = deletion_record.preserve_payment_data
            print(f"✅ preserve_payment_data: {preserve_payment}")
        except Exception as e:
            print(f"❌ Error accessing preserve_payment_data: {str(e)}")
        
        try:
            preserve_profile = deletion_record.preserve_profile_data
            print(f"✅ preserve_profile_data: {preserve_profile}")
        except Exception as e:
            print(f"❌ Error accessing preserve_profile_data: {str(e)}")
        
        try:
            preserve_security = deletion_record.preserve_security_logs
            print(f"✅ preserve_security_logs: {preserve_security}")
        except Exception as e:
            print(f"❌ Error accessing preserve_security_logs: {str(e)}")
        
        # Test 3: Use getattr to access boolean fields
        print("3. Testing getattr boolean field access...")
        preserve_payment_safe = getattr(deletion_record, 'preserve_payment_data', False)
        preserve_profile_safe = getattr(deletion_record, 'preserve_profile_data', False)
        preserve_security_safe = getattr(deletion_record, 'preserve_security_logs', False)
        
        print(f"✅ Safe access - payment: {preserve_payment_safe}, profile: {preserve_profile_safe}, security: {preserve_security_safe}")
        
        # Test 4: Find by token
        print("4. Testing find by token...")
        token = deletion_record.confirmation_token
        found_record = AccountDeletionRecord.find_by_token(token)
        print(f"✅ Found record by token: {found_record.id if found_record else 'None'}")
        
        # Test 5: Access boolean fields on found record
        if found_record:
            print("5. Testing boolean access on found record...")
            try:
                preserve_payment_found = getattr(found_record, 'preserve_payment_data', False)
                print(f"✅ Found record preserve_payment_data: {preserve_payment_found}")
            except Exception as e:
                print(f"❌ Error accessing boolean on found record: {str(e)}")
        
        # Clean up
        deletion_record.delete()
        print("✅ Test record cleaned up")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in boolean access test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the boolean debug test"""
    print("🐛 Debugging Boolean Field Access")
    print("=" * 40)
    
    success = test_boolean_access()
    
    print("\n" + "=" * 40)
    if success:
        print("✅ Boolean field access works correctly!")
    else:
        print("❌ Boolean field access has issues")

if __name__ == "__main__":
    main()
