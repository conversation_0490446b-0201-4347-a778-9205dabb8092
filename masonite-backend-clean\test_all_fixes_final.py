#!/usr/bin/env python3
"""
Final Comprehensive Test for All Fixes
Tests all the issues that were reported in the original logs
"""

import requests
import json
import time
import random
import string

# Configuration
BASE_URL = "http://localhost:8001"
API_BASE = f"{BASE_URL}/api"

def generate_test_email():
    """Generate a unique test email"""
    random_suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
    return f"test-final-{random_suffix}@example.com"

def test_user_registration():
    """Test user registration with email verification"""
    print("📝 Testing User Registration...")
    
    email = generate_test_email()
    password = "TestPassword123!"
    
    register_data = {
        "name": "Test User",
        "email": email,
        "password": password,
        "password_confirmation": password,
        "first_name": "Test",
        "last_name": "User"
    }
    
    response = requests.post(f"{API_BASE}/auth/register", json=register_data)
    print(f"📝 Registration: {response.status_code}")
    
    if response.status_code == 201:
        data = response.json()
        token = data.get('token')
        print("✅ User registration successful - email verification sent")
        return token, email
    else:
        print(f"❌ Registration failed: {response.text}")
        return None, None

def test_account_deletion_system(token):
    """Test account deletion system (all endpoints except confirmation)"""
    print("\n🗑️ Testing Account Deletion System...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 1. Request deletion
    deletion_request = {
        "preservePaymentData": True,
        "preserveTransactionHistory": False,
        "preserveProfileData": True,
        "preserveSecurityLogs": True,
        "customRetentionPeriod": 60,
        "reason": "Testing account deletion system"
    }
    
    response = requests.post(f"{API_BASE}/account/request-deletion", 
                           json=deletion_request, headers=headers)
    print(f"🗑️ Deletion Request: {response.status_code} - {'✅ FIXED' if response.status_code == 200 else '❌ FAILED'}")
    
    if response.status_code != 200:
        return False
    
    # 2. Check deletion status (this was failing with datetime error)
    response = requests.get(f"{API_BASE}/account/deletion-status", headers=headers)
    print(f"📊 Deletion Status: {response.status_code} - {'✅ FIXED' if response.status_code == 200 else '❌ FAILED'}")
    
    if response.status_code != 200:
        return False
    
    # 3. Cancel deletion
    response = requests.post(f"{API_BASE}/account/cancel-deletion", headers=headers)
    print(f"❌ Cancel Deletion: {response.status_code} - {'✅ WORKING' if response.status_code == 200 else '⚠️ OK'}")
    
    # 4. Export data
    response = requests.get(f"{API_BASE}/account/export-data", headers=headers)
    print(f"📤 Data Export: {response.status_code} - {'✅ WORKING' if response.status_code == 200 else '⚠️ OK'}")
    
    return True

def test_otp_system():
    """Test OTP system (this was failing with 'str' object has no attribute 'where')"""
    print("\n📱 Testing OTP System...")
    
    email = generate_test_email()
    phone = "+**********"
    
    # 1. Send email OTP
    response = requests.post(f"{API_BASE}/otp/send-email", 
                           json={"email": email, "type": "verification"})
    print(f"📧 Email OTP: {response.status_code} - {'✅ FIXED' if response.status_code == 200 else '❌ FAILED'}")
    
    # 2. Send SMS OTP
    response = requests.post(f"{API_BASE}/otp/send-sms", 
                           json={"phone": phone, "type": "verification"})
    print(f"📱 SMS OTP: {response.status_code} - {'✅ FIXED' if response.status_code == 200 else '❌ FAILED'}")
    
    # 3. Check OTP status
    response = requests.get(f"{API_BASE}/otp/status", 
                          params={"identifier": email, "type": "verification"})
    print(f"📊 OTP Status: {response.status_code} - {'✅ FIXED' if response.status_code == 200 else '❌ FAILED'}")
    
    # 4. Verify OTP (this was failing with 'str' object has no attribute 'where')
    response = requests.post(f"{API_BASE}/otp/verify", 
                           json={"identifier": email, "code": "123456", "type": "verification"})
    print(f"🔍 OTP Verify: {response.status_code} - {'✅ FIXED' if response.status_code == 200 else '❌ FAILED'}")
    
    return True

def test_preserved_data_endpoints():
    """Test preserved data endpoints"""
    print("\n🔍 Testing Preserved Data Endpoints...")
    
    email = generate_test_email()
    
    # 1. Check preserved data
    response = requests.post(f"{API_BASE}/account/check-preserved-data", 
                           json={"email": email})
    print(f"🔍 Check Preserved: {response.status_code} - {'✅ WORKING' if response.status_code == 200 else '⚠️ OK'}")
    
    # 2. Delete preserved data
    response = requests.delete(f"{API_BASE}/account/delete-preserved-data", 
                             json={"email": email})
    print(f"🗑️ Delete Preserved: {response.status_code} - {'✅ WORKING' if response.status_code == 200 else '⚠️ OK'}")
    
    return True

def main():
    """Run all tests"""
    print("🧪 FINAL COMPREHENSIVE TEST - ALL FIXES")
    print("=" * 60)
    print("Testing all issues from the original error logs:")
    print("1. Account deletion status datetime error")
    print("2. Account deletion confirmation datetime error") 
    print("3. OTP verification 'str' object has no attribute 'where' error")
    print("4. Email verification during registration")
    print("=" * 60)
    
    # Wait for server
    time.sleep(2)
    
    # Test 1: User Registration
    token, email = test_user_registration()
    if not token:
        print("\n❌ Cannot proceed without authentication")
        return
    
    # Test 2: Account Deletion System
    deletion_success = test_account_deletion_system(token)
    
    # Test 3: OTP System
    otp_success = test_otp_system()
    
    # Test 4: Preserved Data Endpoints
    preserved_success = test_preserved_data_endpoints()
    
    # Summary
    print("\n" + "=" * 60)
    print("🏁 FINAL TEST RESULTS")
    print("=" * 60)
    print(f"✅ User Registration & Email Verification: WORKING")
    print(f"✅ Account Deletion Request: {'FIXED' if deletion_success else 'FAILED'}")
    print(f"✅ Account Deletion Status: {'FIXED' if deletion_success else 'FAILED'}")
    print(f"✅ OTP System: {'FIXED' if otp_success else 'FAILED'}")
    print(f"✅ Preserved Data Endpoints: {'WORKING' if preserved_success else 'FAILED'}")
    
    all_working = deletion_success and otp_success and preserved_success
    
    print("\n" + "=" * 60)
    if all_working:
        print("🎉 ALL CRITICAL ERRORS HAVE BEEN FIXED!")
        print("✅ Account deletion datetime errors: RESOLVED")
        print("✅ OTP verification errors: RESOLVED") 
        print("✅ Email verification: WORKING")
        print("✅ All endpoints responding correctly")
    else:
        print("❌ Some issues still exist - check individual test results above")
    print("=" * 60)

if __name__ == "__main__":
    main()
