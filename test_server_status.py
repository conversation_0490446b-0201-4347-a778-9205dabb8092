#!/usr/bin/env python3
"""
Quick server status check
"""

import requests
import json

BASE_URL = "http://localhost:8001"

def test_server_status():
    """Test if server is running"""
    try:
        print("🔍 Testing server status...")
        
        # Test basic server response
        response = requests.get(f"{BASE_URL}/", timeout=5)
        print(f"✅ Server is running - Status: {response.status_code}")
        
        # Test API endpoints
        api_endpoints = [
            "/api/auth/login",
            "/api/auth/register", 
            "/api/security/events",
            "/api/notifications",
            "/api/queue/status"
        ]
        
        for endpoint in api_endpoints:
            try:
                response = requests.get(f"{BASE_URL}{endpoint}", timeout=5)
                print(f"📍 {endpoint}: {response.status_code}")
            except Exception as e:
                print(f"❌ {endpoint}: Error - {str(e)}")
        
        # Test registration with detailed error info
        print("\n🧪 Testing user registration...")
        test_data = {
            "email": "<EMAIL>",
            "password": "TestPassword123!",
            "password_confirmation": "TestPassword123!",
            "name": "Test User"
        }
        
        response = requests.post(f"{BASE_URL}/api/auth/register", json=test_data, timeout=10)
        print(f"Registration Status: {response.status_code}")
        print(f"Response: {response.text}")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ Server is not running or not accessible")
        return False
    except Exception as e:
        print(f"❌ Server test error: {str(e)}")
        return False

if __name__ == "__main__":
    test_server_status()
