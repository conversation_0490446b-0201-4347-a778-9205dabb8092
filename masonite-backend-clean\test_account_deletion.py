#!/usr/bin/env python3
"""Test Account Deletion System Implementation"""

import requests
import json
import sys

# Configuration
BASE_URL = "http://localhost:8001/api"
TEST_USER_EMAIL = "<EMAIL>"
TEST_USER_PASSWORD = "TestPassword123!"

def test_account_deletion_system():
    """Test the complete account deletion system"""
    print("🧪 Testing Account Deletion System Implementation")
    print("=" * 60)
    
    # Step 1: Register a test user
    print("\n1️⃣ Registering test user...")
    register_data = {
        "email": TEST_USER_EMAIL,
        "password": TEST_USER_PASSWORD,
        "password_confirmation": TEST_USER_PASSWORD,
        "name": "Test Deletion"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/register", json=register_data)
        if response.status_code == 201:
            print("✅ User registration successful")
            user_data = response.json()
            token = user_data.get('token')
        else:
            print(f"❌ Registration failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Registration error: {str(e)}")
        return False

    # Step 2: Test account deletion request
    print("\n2️⃣ Testing account deletion request...")
    headers = {"Authorization": f"Bearer {token}"}
    deletion_request = {
        "preservePaymentData": True,
        "preserveTransactionHistory": False,
        "preserveProfileData": True,
        "preserveSecurityLogs": True,
        "customRetentionPeriod": 60,
        "reason": "Testing account deletion system"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/account/request-deletion", 
                               json=deletion_request, headers=headers)
        if response.status_code == 200:
            print("✅ Account deletion request successful")
            deletion_data = response.json()
            print(f"📋 Deletion ID: {deletion_data.get('deletionId')}")
            print(f"🔗 Confirmation Token: {deletion_data.get('confirmationToken', 'N/A')}")
            confirmation_token = deletion_data.get('confirmationToken')
        else:
            print(f"❌ Deletion request failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Deletion request error: {str(e)}")
        return False

    # Step 3: Test deletion status
    print("\n3️⃣ Testing deletion status...")
    try:
        response = requests.get(f"{BASE_URL}/account/deletion-status", headers=headers)
        if response.status_code == 200:
            print("✅ Deletion status check successful")
            status_data = response.json()
            print(f"📊 Has pending deletion: {status_data.get('hasPendingDeletion')}")
            if status_data.get('deletionRecord'):
                print(f"📋 Status: {status_data['deletionRecord'].get('status')}")
        else:
            print(f"❌ Status check failed: {response.status_code}")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"❌ Status check error: {str(e)}")

    # Step 4: Test data export
    print("\n4️⃣ Testing data export...")
    try:
        response = requests.get(f"{BASE_URL}/account/export-data", headers=headers)
        if response.status_code == 200:
            print("✅ Data export successful")
            export_data = response.json()
            print(f"📊 Profile data: {bool(export_data.get('profile'))}")
            print(f"💳 Payment data: {len(export_data.get('payments', []))} records")
            print(f"🔒 Security data: {bool(export_data.get('security_logs'))}")
        else:
            print(f"❌ Data export failed: {response.status_code}")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"❌ Data export error: {str(e)}")

    # Step 5: Test deletion confirmation (if token available)
    if confirmation_token:
        print("\n5️⃣ Testing deletion confirmation...")
        try:
            response = requests.post(f"{BASE_URL}/account/confirm-deletion", 
                                   json={"token": confirmation_token})
            if response.status_code == 200:
                print("✅ Deletion confirmation successful")
                confirm_data = response.json()
                print(f"📋 Message: {confirm_data.get('message')}")
                print(f"🗂️ Preserved data: {confirm_data.get('preservedDataSummary', {})}")
            else:
                print(f"❌ Deletion confirmation failed: {response.status_code}")
                print(f"Response: {response.text}")
        except Exception as e:
            print(f"❌ Deletion confirmation error: {str(e)}")

    # Step 6: Test preserved data check
    print("\n6️⃣ Testing preserved data check...")
    try:
        response = requests.get(f"{BASE_URL}/account/check-preserved-data/{TEST_USER_EMAIL}")
        if response.status_code == 200:
            print("✅ Preserved data check successful")
            preserved_data = response.json()
            print(f"📊 Has preserved data: {preserved_data.get('hasPreservedData')}")
            if preserved_data.get('preservedDataSummary'):
                print(f"🗂️ Summary: {preserved_data['preservedDataSummary']}")
        else:
            print(f"❌ Preserved data check failed: {response.status_code}")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"❌ Preserved data check error: {str(e)}")

    print("\n" + "=" * 60)
    print("🎯 Account Deletion System Test Complete!")
    return True

def test_account_endpoints():
    """Test account deletion endpoints availability"""
    print("\n🔍 Testing Account Deletion Endpoints Availability")
    print("-" * 50)
    
    endpoints = [
        ("POST", "/account/request-deletion", "Request account deletion"),
        ("GET", "/account/deletion-status", "Get deletion status"),
        ("POST", "/account/cancel-deletion", "Cancel deletion"),
        ("GET", "/account/export-data", "Export user data"),
        ("POST", "/account/request-export", "Request data export"),
        ("POST", "/account/confirm-deletion", "Confirm deletion (public)"),
        ("GET", "/account/check-preserved-data/<EMAIL>", "Check preserved data"),
        ("POST", "/account/restore-data", "Restore data"),
        ("DELETE", "/account/delete-preserved-data", "Delete preserved data")
    ]
    
    available_count = 0
    
    for method, endpoint, description in endpoints:
        try:
            if method == "GET":
                response = requests.get(f"{BASE_URL}{endpoint}")
            elif method == "POST":
                response = requests.post(f"{BASE_URL}{endpoint}", json={})
            elif method == "DELETE":
                response = requests.delete(f"{BASE_URL}{endpoint}", json={})
            
            # Check if endpoint exists (not 404)
            if response.status_code != 404:
                print(f"✅ {method} {endpoint} - {description}")
                available_count += 1
            else:
                print(f"❌ {method} {endpoint} - {description} (Not Found)")
                
        except Exception as e:
            print(f"❌ {method} {endpoint} - {description} (Error: {str(e)})")
    
    print(f"\n📊 Available endpoints: {available_count}/{len(endpoints)}")
    return available_count == len(endpoints)

if __name__ == "__main__":
    print("🚀 Starting Account Deletion System Tests")
    
    # Test endpoint availability first
    endpoints_available = test_account_endpoints()
    
    if endpoints_available:
        # Run full system test
        success = test_account_deletion_system()
        sys.exit(0 if success else 1)
    else:
        print("❌ Some endpoints are not available. Please check the implementation.")
        sys.exit(1)
