#!/usr/bin/env python3
"""
Test Payment Creation After CORS Fix - Server Test
"""

import requests
import json
import sys
import time
import threading
import subprocess

def start_server():
    """Start the server in background"""
    try:
        subprocess.run([
            "python", "craft", "serve", "--port", "8001"
        ], cwd=".", check=False)
    except Exception as e:
        print(f"Server start error: {e}")

def test_payment_after_cors():
    """Test payment creation after CORS fix"""
    print("Testing Payment Creation After CORS Fix")
    print("=" * 50)
    
    # Wait for server to start
    print("Waiting for server to start...")
    time.sleep(3)
    
    BASE_URL = "http://localhost:8001"
    API_BASE = f"{BASE_URL}/api"
    
    # Step 1: Test server is running
    try:
        health_response = requests.get(f"{BASE_URL}/", timeout=5)
        print(f"Server health check: {health_response.status_code}")
    except Exception as e:
        print(f"Server not responding: {e}")
        return False
    
    # Step 2: Register a test user
    print("\n1. Registering test user...")
    register_data = {
        "name": "Payment Tester",
        "email": "<EMAIL>",
        "password": "SecurePass123!",
        "password_confirmation": "SecurePass123!"
    }
    
    try:
        register_response = requests.post(f"{API_BASE}/auth/register", json=register_data, timeout=10)
        print(f"   Status: {register_response.status_code}")
        
        if register_response.status_code == 201:
            register_result = register_response.json()
            token = register_result.get('token')
            print(f"   SUCCESS: User registered")
            print(f"   Token: {token[:20]}..." if token else "   ERROR: No token received")
        else:
            print(f"   ERROR: Registration failed: {register_response.text}")
            return False
            
    except Exception as e:
        print(f"   ERROR: Registration error: {str(e)}")
        return False
    
    # Step 3: Test payment creation
    print("\n2. Testing payment creation...")
    payment_data = {
        "amount": 100.50,
        "currency": "INR", 
        "description": "Test payment after CORS fix"
    }
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        payment_response = requests.post(f"{API_BASE}/payments/create-order", 
                                       json=payment_data, 
                                       headers=headers,
                                       timeout=10)
        print(f"   Status: {payment_response.status_code}")
        print(f"   Response: {payment_response.text}")
        
        if payment_response.status_code == 200:
            payment_result = payment_response.json()
            print(f"   SUCCESS: Payment creation successful!")
            print(f"   Order ID: {payment_result.get('orderId', 'N/A')}")
            print(f"   Amount: {payment_result.get('amount', 'N/A')}")
            return True
        else:
            print(f"   ERROR: Payment creation failed")
            return False
            
    except Exception as e:
        print(f"   ERROR: Payment creation error: {str(e)}")
        return False

def main():
    """Run test"""
    print("Payment Creation Test Suite - CORS Fix Verification")
    print("=" * 60)
    
    # Start server in background thread
    server_thread = threading.Thread(target=start_server, daemon=True)
    server_thread.start()
    
    # Test payment creation
    payment_ok = test_payment_after_cors()
    
    print("\n" + "=" * 60)
    print("TEST RESULTS:")
    print(f"   Payment Creation: {'PASS' if payment_ok else 'FAIL'}")
    
    if payment_ok:
        print("\nSUCCESS: Payment creation works after CORS fix!")
        print("The issue has been resolved.")
        return 0
    else:
        print("\nFAILED: Payment creation still not working.")
        print("Check server logs for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
