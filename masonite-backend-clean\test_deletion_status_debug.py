#!/usr/bin/env python3
"""
Debug Account Deletion Status Issue
Isolate and fix the datetime serialization problem
"""

import requests
import json
import time
import random
import string

# Configuration
BASE_URL = "http://localhost:8001"
API_BASE = f"{BASE_URL}/api"

def generate_test_email():
    """Generate a unique test email"""
    random_suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
    return f"test-debug-{random_suffix}@example.com"

def test_deletion_status_issue():
    """Test the specific deletion status issue"""
    print("🔧 Testing Account Deletion Status Issue...")
    
    email = generate_test_email()
    password = "TestPassword123!"
    
    # 1. Register user
    register_data = {
        "name": "Test User",
        "email": email,
        "password": password,
        "password_confirmation": password,
        "first_name": "Test",
        "last_name": "User"
    }
    
    response = requests.post(f"{API_BASE}/auth/register", json=register_data)
    print(f"📝 Registration: {response.status_code}")
    
    if response.status_code != 201:
        print(f"❌ Registration failed: {response.text}")
        return False
    
    data = response.json()
    token = data.get('token')
    headers = {"Authorization": f"Bearer {token}"}
    
    print(f"✅ User registered successfully")
    
    # 2. Request account deletion
    deletion_request = {
        "preservePaymentData": True,
        "preserveTransactionHistory": False,
        "preserveProfileData": True,
        "preserveSecurityLogs": True,
        "customRetentionPeriod": 60,
        "reason": "Testing deletion status debug"
    }
    
    response = requests.post(f"{API_BASE}/account/request-deletion", 
                           json=deletion_request, headers=headers)
    print(f"🗑️ Deletion Request: {response.status_code}")
    
    if response.status_code != 200:
        print(f"❌ Deletion request failed: {response.text}")
        return False
    
    deletion_data = response.json()
    print(f"✅ Deletion requested successfully")
    
    # 3. Check deletion status (this is where the error occurs)
    print("\n🔍 Testing deletion status endpoint...")
    response = requests.get(f"{API_BASE}/account/deletion-status", headers=headers)
    print(f"📊 Deletion Status Response Code: {response.status_code}")
    print(f"📊 Deletion Status Response: {response.text}")
    
    if response.status_code == 200:
        status_data = response.json()
        print(f"✅ Deletion status retrieved successfully")
        print(f"📊 Has Pending Deletion: {status_data.get('hasPendingDeletion')}")
        
        deletion_record = status_data.get('deletionRecord')
        if deletion_record:
            print(f"📊 Deletion ID: {deletion_record.get('deletion_id')}")
            print(f"📊 Status: {deletion_record.get('status')}")
            print(f"📊 Requested At: {deletion_record.get('requested_at')}")
            print(f"📊 Expires At: {deletion_record.get('expires_at')}")
        
        return True
    else:
        print(f"❌ Deletion status check failed: {response.text}")
        return False

def main():
    """Run the debug test"""
    print("🐛 Debugging Account Deletion Status Issue")
    print("=" * 50)
    
    # Wait a moment for server to start
    time.sleep(2)
    
    success = test_deletion_status_issue()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ Deletion status issue has been FIXED!")
    else:
        print("❌ Deletion status issue still exists")

if __name__ == "__main__":
    main()
