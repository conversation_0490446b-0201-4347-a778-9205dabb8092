"""CreatePaymentsTable Migration."""

from masoniteorm.migrations import Migration


class CreatePaymentsTable(Migration):
    def up(self):
        """
        Run the migrations.
        """
        with self.schema.create("payments") as table:
            table.increments("id")

            table.timestamps()

    def down(self):
        """
        Revert the migrations.
        """
        self.schema.drop("payments")
